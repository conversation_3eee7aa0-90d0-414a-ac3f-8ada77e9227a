# 🚀 Third-Party Services Setup Guide

## Current Status ✅
Your server is now running successfully with the following services:
- ✅ **MongoDB Atlas** - Connected and working
- ✅ **Cloudinary** - Configured for image uploads
- ✅ **Express Server** - Running on port 5000
- ⚠️ **Twilio** - Not configured (SMS functionality disabled)
- ⚠️ **Razorpay** - Not configured (payment processing disabled)
- ⚠️ **ngrok** - Not configured (webhook testing disabled)

## Quick Start Commands

### For Basic Development (Recommended)
```bash
npm run dev:simple
```
This starts your server without webhook functionality - perfect for frontend development and API testing.

### For Webhook Development (Requires ngrok setup)
```bash
npm run webhook:dev
```
This starts your server with ngrok tunnel for webhook testing.

---

## Optional Service Configuration

### 1. 🔔 Twilio Configuration (SMS/OTP)
**Required for:** SMS OTP verification, login alerts, welcome messages

1. Sign up at [Twilio Console](https://console.twilio.com/)
2. Get your credentials:
   - Account SID (starts with "AC")
   - Auth Token
   - Phone Number

3. Update your `.env` file:
```env
TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+**********
```

**Test SMS functionality:**
```bash
# OTP will be logged to console in development mode even without Twilio
npm run dev:simple
```

### 2. 💳 Razorpay Configuration (Payments)
**Required for:** Payment processing, order management

1. Sign up at [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Get your API keys (test/live):
   - Key ID (starts with "rzp_")
   - Key Secret
   - Webhook Secret

3. Update your `.env` file:
```env
# For Development (Test Mode)
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### 3. 🌐 ngrok Configuration (Webhook Testing)
**Required for:** Testing payment webhooks, external API callbacks

1. Sign up at [ngrok Dashboard](https://dashboard.ngrok.com/signup)
2. Install ngrok:
```bash
npm install -g @ngrok/ngrok
```

3. Set up auth token:
```bash
ngrok config add-authtoken YOUR_TOKEN_HERE
```

4. Start webhook development:
```bash
npm run webhook:dev
```

### 4. 📧 Email Configuration (Already Configured)
Your SMTP settings are already configured in `.env`:
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=qckvrgqocyeyvxzx
```

---

## Development Workflows

### Frontend Development
```bash
npm run dev:simple
```
- Server runs on `http://localhost:5000`
- API available at `http://localhost:5000/api`
- No external dependencies required

### Payment Integration Testing
1. Configure Razorpay (test keys)
2. Configure ngrok
3. Run: `npm run webhook:dev`
4. Update Razorpay webhook URL with ngrok URL

### SMS Testing
- Without Twilio: OTP codes are logged to console
- With Twilio: Real SMS sent to phone numbers

---

## Error Troubleshooting

### Twilio Errors
- `accountSid must start with AC` → Check your Account SID format
- `Authentication failed` → Verify Auth Token

### ngrok Errors
- `authentication failed` → Run `ngrok config add-authtoken YOUR_TOKEN`
- `command not found` → Install ngrok: `npm install -g @ngrok/ngrok`

### Razorpay Errors
- `Invalid API key` → Check your Key ID and Secret
- `Webhook signature verification failed` → Verify webhook secret

---

## Production Deployment

### Environment Variables
Update `.env` for production:
```env
NODE_ENV=production
FRONTEND_URL=https://yourdomain.com
JWT_SECRET=your_super_secure_jwt_secret
```

### Service Configuration
1. Use production/live API keys for all services
2. Set up proper webhook URLs (not ngrok)
3. Configure proper CORS settings
4. Set up SSL certificates

---

## Support

If you need help configuring any of these services, refer to:
- [Twilio Documentation](https://www.twilio.com/docs)
- [Razorpay Documentation](https://razorpay.com/docs/)
- [ngrok Documentation](https://ngrok.com/docs)
- [Cloudinary Documentation](https://cloudinary.com/documentation)

Your server is fully functional for development without these optional services!
