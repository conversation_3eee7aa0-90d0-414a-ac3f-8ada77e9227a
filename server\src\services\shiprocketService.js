const axios = require('axios');
const Vendor = require('../models/Vendor');

class ShiprocketService {
  constructor() {
    this.baseURL = 'https://apiv2.shiprocket.in/v1/external';
    this.token = null;
    this.tokenExpiry = null;
  }

  // Get authentication token
  async authenticate() {
    try {
      const response = await axios.post(`${this.baseURL}/auth/login`, {
        email: process.env.SHIPROCKET_EMAIL,
        password: process.env.SHIPROCKET_PASSWORD
      });

      this.token = response.data.token;
      this.tokenExpiry = new Date(Date.now() + (9 * 24 * 60 * 60 * 1000));
      return this.token;
    } catch (error) {
      console.error('Shiprocket authentication failed:', error.response?.data || error.message);
      throw new Error('Failed to authenticate with Shiprocket');
    }
  }

  // Get headers with authentication
  async getAuthHeaders() {
    if (!this.token || new Date() >= this.tokenExpiry) {
      await this.authenticate();
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }

  // Create order with vendor's address as pickup
  async createOrder(orderData) {
    try {
      const headers = await this.getAuthHeaders();
      
      // Get first vendor from items to use their address
      const firstItem = orderData.items[0];
      const vendor = await Vendor.findById(firstItem.vendor);
      
      const shiprocketOrder = {
        order_id: orderData.orderNumber,
        order_date: new Date().toISOString().split('T')[0],
        // Use vendor's business address as pickup
        pickup_location: vendor ? vendor.businessName : 'Primary',
        billing_customer_name: `${orderData.billing.firstName} ${orderData.billing.lastName}`,
        billing_address: orderData.billing.address.street,
        billing_city: orderData.billing.address.city,
        billing_pincode: String(orderData.billing.address.zipCode),
        billing_state: orderData.billing.address.state,
        billing_country: orderData.billing.address.country || 'India',
        billing_email: orderData.billing.email,
        billing_phone: String(orderData.billing.phone),
        shipping_is_billing: false,
        shipping_customer_name: `${orderData.shipping.firstName} ${orderData.shipping.lastName}`,
        shipping_address: orderData.shipping.address.street,
        shipping_city: orderData.shipping.address.city,
        shipping_pincode: String(orderData.shipping.address.zipCode),
        shipping_state: orderData.shipping.address.state,
        shipping_country: orderData.shipping.address.country || 'India',
        shipping_phone: String(orderData.shipping.phone || orderData.billing.phone),
        order_items: orderData.items.map(item => ({
          name: item.name,
          sku: item.sku,
          units: item.quantity,
          selling_price: parseFloat(item.unitPrice || item.totalPrice / item.quantity),
        })),
        payment_method: orderData.payment.method === 'cod' ? 'COD' : 'Prepaid',
        shipping_charges: parseFloat(orderData.pricing.shipping || 0),
        sub_total: parseFloat(orderData.pricing.subtotal),
        length: 10,
        breadth: 10,
        height: 10,
        weight: 0.5
      };

      const response = await axios.post(`${this.baseURL}/orders/create/adhoc`, shiprocketOrder, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to create Shiprocket order:', error.response?.data || error.message);
      throw new Error(`Failed to create order in Shiprocket: ${error.response?.data?.message || error.message}`);
    }
  }

  // Track shipment
  async trackShipment(awb) {
    try {
      const headers = await this.getAuthHeaders();
      const response = await axios.get(`${this.baseURL}/courier/track/awb/${awb}`, { headers });
      return response.data;
    } catch (error) {
      console.error('Failed to track shipment:', error.response?.data || error.message);
      throw new Error(`Failed to track shipment: ${error.response?.data?.message || error.message}`);
    }
  }
}

module.exports = new ShiprocketService();
