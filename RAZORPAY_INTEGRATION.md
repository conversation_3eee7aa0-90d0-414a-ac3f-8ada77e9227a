# Simple Razorpay Integration

## What This Does
Simple Razorpay Standard Checkout integration. Razorpay handles everything - UI, payments, security.

## Features
- ✅ Razorpay Standard Checkout
- ✅ Payment Verification
- ✅ Works with your existing Order system
- ✅ Simple React component

## Setup Instructions

### 1. Environment Configuration

Update your `.env` file with Razorpay credentials:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret_here
```

### 2. Get Razorpay Credentials

1. Sign up at [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Navigate to Settings → API Keys
3. Generate Test/Live keys
4. Copy Key ID and Key Secret to your `.env` file
5. For webhooks, create a webhook endpoint and copy the secret

### 3. Frontend Setup

Add Razorpay checkout script to your HTML head:

```html
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
```

## API Endpoints

### 1. Create Razorpay Order
```http
POST /api/payments/razorpay/create-order
Authorization: Bearer <token>
Content-Type: application/json

{
  "items": [
    {
      "productId": "64a1b2c3d4e5f6789012345",
      "quantity": 2,
      "variant": {
        "name": "Color",
        "options": [{"name": "color", "value": "red"}]
      }
    }
  ],
  "billing": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": {
      "street": "123 Main St",
      "city": "Mumbai",
      "state": "Maharashtra",
      "zipCode": "400001",
      "country": "India"
    }
  },
  "shipping": {
    "firstName": "John",
    "lastName": "Doe",
    "address": {
      "street": "123 Main St",
      "city": "Mumbai",
      "state": "Maharashtra",
      "zipCode": "400001",
      "country": "India"
    }
  },
  "shippingMethod": "standard",
  "currency": "INR",
  "customerNotes": "Please deliver fast",
  "clearCart": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "razorpayOrder": {
      "id": "order_ABC123",
      "amount": 59000,
      "currency": "INR",
      "receipt": "ORD250107123456"
    },
    "order": {
      "id": "64a1b2c3d4e5f6789012345",
      "orderNumber": "ORD250107123456",
      "total": 590,
      "currency": "INR"
    },
    "key": "rzp_test_1234567890"
  }
}
```

### 2. Verify Payment
```http
POST /api/payments/razorpay/verify
Authorization: Bearer <token>
Content-Type: application/json

{
  "razorpay_payment_id": "pay_ABC123",
  "razorpay_order_id": "order_ABC123",
  "razorpay_signature": "generated_signature",
  "order_id": "64a1b2c3d4e5f6789012345"
}
```

### 3. Webhook Endpoint
```http
POST /api/payments/razorpay/webhook
X-Razorpay-Signature: webhook_signature
Content-Type: application/json

{
  "event": "payment.captured",
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_ABC123",
        "notes": {
          "order_number": "ORD250107123456"
        }
      }
    }
  }
}
```

### 4. Get Payment Status
```http
GET /api/payments/status/64a1b2c3d4e5f6789012345
Authorization: Bearer <token>
```

### 5. Get Payment Methods
```http
GET /api/payments/methods
```

## Frontend Usage

### Using the RazorpayStandardCheckout Component

```jsx
import RazorpayStandardCheckout from './components/customer/RazorpayCheckout';

const CheckoutPage = () => {
  const orderData = {
    items: [
      {
        productId: "64a1b2c3d4e5f6789012345",
        quantity: 1
      }
    ],
    billing: {
      firstName: "John",
      lastName: "Doe",
      email: "<EMAIL>",
      phone: "1234567890",
      address: {
        street: "123 Main St",
        city: "Mumbai",
        state: "Maharashtra",
        zipCode: "400001",
        country: "India"
      }
    },
    shipping: {
      firstName: "John",
      lastName: "Doe",
      address: {
        street: "123 Main St",
        city: "Mumbai",
        state: "Maharashtra",
        zipCode: "400001",
        country: "India"
      }
    }
  };

  const handlePaymentSuccess = (data) => {
    console.log('Payment successful:', data);
    // Redirect to order confirmation page
    navigate(`/orders/${data.orderId}`);
  };

  const handlePaymentFailure = (error) => {
    console.error('Payment failed:', error);
    // Handle payment failure
  };

  return (
    <RazorpayCheckout
      orderData={orderData}
      onPaymentSuccess={handlePaymentSuccess}
      onPaymentFailure={handlePaymentFailure}
    />
  );
};
```

## Database Schema Updates

### Order Model
The existing Order model has been updated to include `'razorpay'` in the payment method enum:

```javascript
payment: {
  method: {
    type: String,
    enum: ['credit_card', 'debit_card', 'paypal', 'stripe', 'razorpay', 'bank_transfer', 'cash_on_delivery', 'cod'],
    required: true
  }
  // ... other fields
}
```

## Webhook Configuration

### Setting up Webhooks in Razorpay Dashboard

1. Go to Settings → Webhooks in your Razorpay dashboard
2. Add webhook URL: `https://yourdomain.com/api/payments/razorpay/webhook`
3. Select events:
   - `payment.captured`
   - `payment.failed`
   - `order.paid`
4. Set webhook secret and update your `.env` file

### Supported Webhook Events

- **payment.captured**: Payment has been successfully captured
- **payment.failed**: Payment has failed
- **order.paid**: Order has been marked as paid

## Security Features

1. **Signature Verification**: All payments are verified using HMAC SHA256
2. **Webhook Validation**: Webhooks are validated using webhook secret
3. **Order Validation**: Orders are validated before payment processing
4. **Stock Verification**: Inventory is checked before order creation
5. **User Authorization**: All endpoints require proper authentication

## Error Handling

### Common Error Responses

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error (development only)"
}
```

### Error Codes

- **400**: Bad Request - Invalid data or missing parameters
- **401**: Unauthorized - Invalid or missing token
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **500**: Internal Server Error - Server error

## Testing

### Test Credentials
Use these test cards in Razorpay's test mode:

- **Success**: 4111 1111 1111 1111
- **Failure**: 4000 0000 0000 0002
- **CVV**: Any 3-digit number
- **Expiry**: Any future date

### Testing Flow

1. Create an order using test credentials
2. Use test card details for payment
3. Verify webhook receives events
4. Check order status updates in database

## Multi-Vendor Support

The integration supports multi-vendor functionality:

1. **Commission Calculation**: Automatic platform and vendor commission calculation
2. **Vendor Analytics**: Sales tracking per vendor
3. **Split Payments**: Future enhancement for direct vendor payments

## Production Deployment

### Checklist

- [ ] Update environment variables with live Razorpay keys
- [ ] Configure webhook URLs for production domain
- [ ] Test payment flow with small amounts
- [ ] Monitor webhook delivery
- [ ] Set up proper logging and monitoring
- [ ] Configure SSL certificates
- [ ] Test error scenarios

### Environment Variables for Production

```env
RAZORPAY_KEY_ID=rzp_live_your_live_key_id
RAZORPAY_KEY_SECRET=your_live_secret_key
RAZORPAY_WEBHOOK_SECRET=your_live_webhook_secret
```

## Monitoring and Logs

### Important Logs to Monitor

1. Payment creation attempts
2. Verification failures
3. Webhook deliveries
4. Order status changes
5. Inventory updates

### Dashboard Monitoring

Monitor these metrics in Razorpay dashboard:
- Payment success rate
- Failed payment reasons
- Webhook delivery status
- Settlement reports

## Support

For issues or questions:

1. Check Razorpay documentation: https://razorpay.com/docs/
2. Review server logs for error details
3. Test in development environment first
4. Contact Razorpay support for payment gateway issues

## Changelog

### v1.0.0
- Initial Razorpay integration
- Order creation and verification
- Webhook support
- Multi-vendor commission calculation
- Frontend React component
- Complete documentation

---

**Note**: Always test the integration thoroughly in the test environment before going live. Keep your API keys secure and never commit them to version control.
