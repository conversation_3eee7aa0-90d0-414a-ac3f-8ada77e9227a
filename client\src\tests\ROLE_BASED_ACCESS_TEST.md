# Role-Based Access Control Test

## Overview
This document outlines the expected behavior for role-based access control in the multi-vendor eCommerce application.

## Test Scenarios

### 1. Admin Access Control

#### Admin User Accessing Admin Dashboard
- **Route**: `/admin` or `/admin/dashboard`
- **Expected**: ✅ Access granted, admin dashboard loads
- **User Type**: `admin`

#### Vendor User Accessing Admin Dashboard
- **Route**: `/admin` or `/admin/dashboard`
- **Expected**: ❌ Access denied, shows "Access Denied" message
- **User Type**: `vendor`
- **Redirect**: Shows error message with options to go back or home

#### Customer User Accessing Admin Dashboard
- **Route**: `/admin` or `/admin/dashboard`
- **Expected**: ❌ Access denied, shows "Access Denied" message
- **User Type**: `customer`
- **Redirect**: Shows error message with options to go back or home

#### Unauthenticated User Accessing Admin Dashboard
- **Route**: `/admin` or `/admin/dashboard`
- **Expected**: 🔄 Redirected to `/admin/auth`
- **User Type**: `null` (not logged in)

### 2. Vendor Access Control

#### Vendor User Accessing Vendor Dashboard
- **Route**: `/vendor` or `/vendor/dashboard`
- **Expected**: ✅ Access granted, vendor dashboard loads
- **User Type**: `vendor`

#### Admin User Accessing Vendor Dashboard
- **Route**: `/vendor` or `/vendor/dashboard`
- **Expected**: ❌ Access denied, shows "Access Denied" message
- **User Type**: `admin`
- **Redirect**: Shows error message with options to go back or home

#### Customer User Accessing Vendor Dashboard
- **Route**: `/vendor` or `/vendor/dashboard`
- **Expected**: ❌ Access denied, shows "Access Denied" message
- **User Type**: `customer`
- **Redirect**: Shows error message with options to go back or home

#### Unauthenticated User Accessing Vendor Dashboard
- **Route**: `/vendor` or `/vendor/dashboard`
- **Expected**: 🔄 Redirected to `/` (landing page)
- **User Type**: `null` (not logged in)

### 3. Admin Auth Page Access

#### Any User Accessing Admin Auth
- **Route**: `/admin/auth`
- **Expected**: ✅ Access granted (public route for admin login)
- **User Type**: Any or none

#### Already Authenticated Admin
- **Route**: `/admin/auth`
- **Expected**: 🔄 Redirected to `/admin/dashboard`
- **User Type**: `admin`

## Implementation Details

### Components Created
1. **AdminProtectedRoute** (`/src/components/AdminProtectedRoute.jsx`)
   - Checks if user is authenticated
   - Verifies user has admin role (`userType === 'admin'`)
   - Shows loading spinner during auth check
   - Redirects unauthenticated users to `/admin/auth`
   - Shows access denied message for non-admin users

2. **VendorProtectedRoute** (`/src/components/VendorProtectedRoute.jsx`)
   - Checks if user is authenticated
   - Verifies user has vendor role (`userType === 'vendor'`)
   - Shows loading spinner during auth check
   - Redirects unauthenticated users to `/` (landing page)
   - Shows access denied message for non-vendor users

### Routes Protected
- `/admin` - Protected by `AdminProtectedRoute`
- `/admin/dashboard` - Protected by `AdminProtectedRoute`
- `/vendor` - Protected by `VendorProtectedRoute`
- `/vendor/dashboard` - Protected by `VendorProtectedRoute`

### Routes NOT Protected (Public)
- `/admin/auth` - Public admin login page

## Testing Instructions

### Manual Testing
1. **Test as Admin**:
   - Login as admin user
   - Try accessing `/admin/dashboard` - should work
   - Try accessing `/vendor/dashboard` - should show access denied

2. **Test as Vendor**:
   - Login as vendor user
   - Try accessing `/vendor/dashboard` - should work
   - Try accessing `/admin/dashboard` - should show access denied

3. **Test as Customer**:
   - Login as customer user
   - Try accessing `/admin/dashboard` - should show access denied
   - Try accessing `/vendor/dashboard` - should show access denied

4. **Test Unauthenticated**:
   - Logout or use incognito mode
   - Try accessing `/admin/dashboard` - should redirect to `/admin/auth`
   - Try accessing `/vendor/dashboard` - should redirect to `/`

### Expected Error Messages
- **Access Denied for Admin Dashboard**: "You don't have permission to access the admin dashboard. Only administrators can access this area."
- **Access Denied for Vendor Dashboard**: "You don't have permission to access the vendor dashboard. Only vendors can access this area."

## Security Notes
- Role checking is done on both `userType` and `user.role` for compatibility
- All protected routes show loading spinner during authentication check
- Clear error messages help users understand access restrictions
- Proper redirects guide users to appropriate login pages