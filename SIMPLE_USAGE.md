# Simple Razorpay Usage

## 1. Add to .env
```env
RAZORPAY_KEY_ID=rzp_test_your_key
RAZORPAY_KEY_SECRET=your_secret
```

## 2. Add script to HTML
```html
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
```

## 3. Use Component
```jsx
import RazorpayCheckout from './components/customer/RazorpayCheckout';

const orderData = {
  items: [{ productId: "product_id", quantity: 1 }],
  billing: { firstName: "John", lastName: "Doe", email: "<EMAIL>", phone: "1234567890", address: {...} },
  shipping: { firstName: "John", lastName: "Doe", address: {...} }
};

<RazorpayCheckout 
  orderData={orderData}
  onSuccess={(data) => console.log('Success:', data)}
  onFailure={(error) => console.log('Failed:', error)}
/>
```

## That's it! 

Ra<PERSON>pay handles:
- ✅ Payment UI
- ✅ All payment methods (Cards, UPI, Wallets, etc.)
- ✅ Security & PCI compliance
- ✅ Mobile responsiveness
- ✅ Fraud detection

Your backend handles:
- ✅ Order creation
- ✅ Payment verification
- ✅ Inventory updates
- ✅ Commission calculation
