const express = require('express');
const router = express.Router();
const {
  createRazorpayOrder,
  verifyRazorpayPayment,
  handleRazorpayWebhook,
  getPaymentStatus
} = require('../controllers/razorpayController');
const { verifyToken, requireUserType } = require('../middleware/auth/authMiddleware');

// Create Razorpay order
router.post('/razorpay/create-order', verifyToken, requireUserType('customer'), createRazorpayOrder);

// Verify Razorpay payment
router.post('/razorpay/verify', verifyToken, requireUserType('customer'), verifyRazorpayPayment);

// Razorpay webhook
router.post('/razorpay/webhook', handleRazorpayWebhook);

// Get payment status
router.get('/status/:orderId', verifyToken, requireUserType('customer'), getPaymentStatus);

// Get payment methods
router.get('/methods', (req, res) => {
  res.json({
    success: true,
    data: {
      methods: [
        {
          id: 'razorpay',
          name: '<PERSON><PERSON>pa<PERSON>',
          description: 'Pay with cards, UPI, wallets & more',
          enabled: true
        },
        {
          id: 'cod',
          name: 'Cash on Delivery',
          description: 'Pay when you receive',
          enabled: true
        }
      ]
    }
  });
});

module.exports = router;
