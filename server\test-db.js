require('dotenv').config();
const mongoose = require('mongoose');
const HomepageSettings = require('./src/models/HomepageSettings');

async function testDatabase() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/multi-vendor-ecommerce');
    console.log('✅ Connected to MongoDB');

    console.log('🔍 Testing HomepageSettings model...');
    let settings = await HomepageSettings.getSettings();
    console.log('📊 Settings found:', !!settings);

    if (!settings.allCategoriesModal || !settings.allCategoriesModal.mainCategories || settings.allCategoriesModal.mainCategories.length === 0) {
      console.log('🔄 Initializing default categories...');
      await settings.initializeDefaultCategories();
      console.log('✅ Categories initialized');
    } else {
      console.log(`📋 Found ${settings.allCategoriesModal.mainCategories.length} main categories`);
      console.log(`📋 Found ${settings.allCategoriesModal.popularCategories?.length || 0} popular categories`);
    }

    console.log('🧪 Testing virtual methods...');
    const activeMain = settings.activeMainCategories;
    const activePopular = settings.activePopularCategories;
    console.log(`📊 Active main categories: ${activeMain?.length || 0}`);
    console.log(`📊 Active popular categories: ${activePopular?.length || 0}`);

    if (activeMain && activeMain.length > 0) {
      console.log('✅ Virtual methods working correctly');
      console.log('📋 Sample main category:', activeMain[0]);
    }

    console.log('✅ Database test completed successfully');
  } catch (error) {
    console.error('❌ Database test failed:', error);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

testDatabase();