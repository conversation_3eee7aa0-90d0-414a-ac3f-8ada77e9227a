import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { message, notification } from 'antd';
import ProductInfoCard from '../components/ProductInfoCard';
import SuggestedProducts from '../components/SuggestedProducts';
import ReviewForm from '../components/reviews/ReviewForm';
import ReviewList from '../components/reviews/ReviewList';
import Footer from '../components/Footer';
import { productsApi } from '../services/publicApi';
import { reviewService } from '../services/reviewService';
import AuthContext from '../contexts/AuthContext';

const ProductDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useContext(AuthContext);
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // Review states
  const [reviews, setReviews] = useState([]);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [canReview, setCanReview] = useState(false);
  const [reviewPagination, setReviewPagination] = useState(null);
  const [reviewRating, setReviewRating] = useState(null);

  // Load reviews
  const loadReviews = async (page = 1, sortBy = 'newest') => {
    if (!id) return;
    
    setReviewsLoading(true);
    try {
      const data = await reviewService.getProductReviews(id, { page, sortBy });
      console.log('Reviews API Response:', data); // Debug log
      
      setReviews(data.reviews || []);
      setReviewPagination(data.pagination || null);
      
      // Extract rating statistics from response
      if (data.rating || data.statistics) {
        setReviewRating(data.rating || data.statistics);
      } else {
        // Create rating statistics from available data
        // Use pagination.totalReviews for accurate count, but current page reviews for calculations
        const totalReviews = data.pagination?.totalReviews || data.reviews?.length || 0;
        const currentPageReviews = data.reviews || [];
        
        if (totalReviews > 0 && currentPageReviews.length > 0) {
          // For current page calculations
          const totalRating = currentPageReviews.reduce((sum, review) => sum + review.rating, 0);
          const averageRating = totalRating / currentPageReviews.length;
          
          // Calculate rating distribution for current page only
          // Note: This is limited data - ideally API should provide full distribution
          const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
          currentPageReviews.forEach(review => {
            ratingDistribution[review.rating]++;
          });
          
          setReviewRating({
            averageRating: parseFloat(averageRating.toFixed(1)),
            totalReviews,
            ratingDistribution
          });
        } else {
          setReviewRating({
            averageRating: 0,
            totalReviews: 0,
            ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
          });
        }
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      message.error('Failed to load reviews');
    } finally {
      setReviewsLoading(false);
    }
  };

  // Check if user can review
  const checkCanReview = async () => {
    // Only check if user is authenticated and is a customer
    if (!isAuthenticated || !user || user.userType !== 'customer' || !id) {
      setCanReview(false);
      return;
    }

    // Additional check to ensure token exists
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    if (!token) {
      console.log('No auth token found for review check');
      setCanReview(false);
      return;
    }

    try {
      console.log('Checking if user can review product:', id);
      console.log('Current user:', { id: user._id, email: user.email, name: `${user.firstName} ${user.lastName}` });
      
      // First check locally if user has already reviewed by looking at existing reviews
      const hasAlreadyReviewedLocally = reviews.some(review => 
        review.customer && review.customer._id === user._id
      );
      
      if (hasAlreadyReviewedLocally) {
        console.log('✅ User has already reviewed this product (found in local reviews)');
        setCanReview(false);
        return;
      }
      
      // If not found locally, check with server
      const response = await reviewService.canReviewProduct(id);
      console.log('Can review API response:', response);
      
      setCanReview(response.canReview === true);
      
    } catch (error) {
      console.error('Error checking review eligibility:', error);
      
      // Fallback: check if user has already reviewed from existing reviews
      const hasAlreadyReviewedLocally = reviews.some(review => 
        review.customer && review.customer._id === user._id
      );
      
      if (hasAlreadyReviewedLocally) {
        console.log('✅ User has already reviewed (fallback local check)');
        setCanReview(false);
      } else {
        // If API fails and no existing review found locally, be conservative and don't show form
        console.log('⚠️ API failed and no local review found, hiding review form to be safe');
        setCanReview(false);
      }
    }
  };

  // Handle review submission
  const handleReviewSubmit = async (reviewData) => {
    try {
      console.log('Submitting review data:', reviewData);
      const response = await reviewService.createReview(reviewData);
      console.log('Review submission response:', response);
      
      // Immediately hide the review form
      setCanReview(false);
      
      // Show success notification
      notification.success({
        message: 'Review Submitted Successfully!',
        description: 'Thank you for your feedback. Your review will help other customers make informed decisions.',
        placement: 'topRight',
        duration: 4,
      });
      
      // Refresh reviews after a short delay to ensure the new review is available
      setTimeout(() => {
        loadReviews();
      }, 1000);
      
      return { success: true };
    } catch (error) {
      console.error('Error submitting review:', error);
      
      // Show error notification
      notification.error({
        message: 'Review Submission Failed',
        description: error.message || 'Failed to submit review. Please try again.',
        placement: 'topRight',
        duration: 5,
      });
      
      throw error; // Let the form handle the error too
    }
  };

  // Handle review pagination
  const handleReviewPageChange = (page) => {
    loadReviews(page);
  };

  // Handle review sorting
  const handleReviewSortChange = (sortBy) => {
    loadReviews(1, sortBy);
  };

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);

        // Fetch product from the real database
        const response = await productsApi.getProduct(id);

        if (!response.data.success) {
          throw new Error('Product not found');
        }

        // Handle both single product and nested product response
        const productData = response.data.data.product || response.data.data;

        // Transform API data to match our ProductInfoCard component structure
        const transformedProduct = {
          id: productData._id,
          name: productData.name,
          brand: productData.vendor?.businessName || productData.category?.name || "Unknown Brand",
          sku: `SKU-${productData._id}`,
          price: productData.pricing?.salePrice || productData.pricing?.basePrice || 0,
          originalPrice: productData.pricing?.basePrice || productData.pricing?.salePrice || 0,
          discount: productData.pricing?.basePrice && productData.pricing?.salePrice ?
            Math.round(((productData.pricing.basePrice - productData.pricing.salePrice) / productData.pricing.basePrice) * 100) : 0,
          rating: productData.reviews?.averageRating || 0,
          reviewCount: productData.reviews?.totalReviews || 0,
          inStock: productData.inventory?.quantity > 0,
          stockCount: productData.inventory?.quantity || 0,
          images: productData.images || [],
          variants: productData.variants || [
            { id: 1, name: "Default", color: "#000000", available: true }
          ],
          badges: ["Free Shipping"],
          description: productData.description || "No description available for this product.",
          specifications: {
            "General": {
              "Category": productData.category?.name || "N/A",
              "Product ID": productData._id || "N/A",
              "Availability": productData.inventory?.quantity > 0 ? "In Stock" : "Out of Stock"
            },
            "Details": {
              "Brand": productData.vendor?.businessName || "Unknown",
              "Model": `Model-${productData._id}`,
              "Warranty": "1 Year"
            }
          },
          features: [
            "High Quality Materials",
            "Durable Construction",
            "Modern Design",
            "Easy to Use"
          ],
          shipping: {
            freeShipping: true,
            estimatedDays: "2-3",
            returnPolicy: "30-day return"
          }
        };

        setProduct(transformedProduct);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError(err.message);
        message.error('Failed to load product details');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProduct();
    }
  }, [id]);

  // Load reviews when product is loaded
  useEffect(() => {
    if (product) {
      loadReviews();
    }
  }, [product]);

  // Check review eligibility after reviews are loaded and user state changes
  useEffect(() => {
    if (product && reviews.length >= 0) { // Check even if reviews is empty array
      checkCanReview();
    }
  }, [product, reviews, isAuthenticated, user]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading product details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Product Not Found</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => navigate('/')}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Back to Home
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      
      {/* Breadcrumb */}
      <div className="max-w-7xl mx-auto px-4 py-2">
        <nav className="text-sm text-gray-500">
          <button 
            onClick={() => navigate('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span className="mx-2">/</span>
          <button 
            onClick={() => navigate('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Products
          </button>
          <span className="mx-2">/</span>
          <span className="text-gray-800">{product?.name}</span>
        </nav>
      </div>

      <ProductInfoCard product={product} />
      
      {/* Reviews Section */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Review Form - Only show for eligible customers */}
        {canReview && isAuthenticated && user?.userType === 'customer' && (
          <ReviewForm
            productId={id}
            productName={product?.name}
            onReviewSubmit={handleReviewSubmit}
          />
        )}
        
        {/* Reviews List */}
        <ReviewList
          productId={id}
          reviews={reviews}
          rating={reviewRating}
          loading={reviewsLoading}
          pagination={reviewPagination}
          onPageChange={handleReviewPageChange}
          onSortChange={handleReviewSortChange}
        />
      </div>
      
      {/* Suggested Products Section */}
      <div className="bg-white">
        <SuggestedProducts 
          currentProductId={id}
          categoryName={product?.brand}
          maxProducts={8}
        />
      </div>
      
      <Footer />
    </div>
  );
};

export default ProductDetailPage;