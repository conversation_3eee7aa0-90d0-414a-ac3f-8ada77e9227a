import React, { useEffect } from "react";
import { useCart } from "../contexts/CartContext";
import Footer from "../components/Footer";
import { getPrimaryProductImage } from "../utils/imageUtils";
import { useCurrency } from "../contexts/CurrencyContext";

// Cart Item Component
const CartItem = ({ item, removeFromCart, updateCartItem, loading }) => {
  const { getCurrencySymbol } = useCurrency();
  const handleRemove = () => {
    removeFromCart(item.product._id, item.selectedVariant?.sku);
  };
  
  const decrementQuantity = () => {
    if (item.quantity > 1) {
      updateCartItem(item.product._id, item.quantity - 1, item.selectedVariant?.sku);
    }
  };

  const incrementQuantity = () => {
    updateCartItem(item.product._id, item.quantity + 1, item.selectedVariant?.sku);
  };

  const productImage = getPrimaryProductImage(item.product) || 'https://via.placeholder.com/100x100?text=No+Image';

  const itemPrice = item.selectedVariant?.price || item.priceAtAdd;
  const itemTotal = itemPrice * item.quantity;
  const originalPrice = itemPrice * 1.2; // Simulated original price for discount effect
  const discount = Math.round(((originalPrice - itemPrice) / originalPrice) * 100);

  return (
    <div className="bg-white border-b border-gray-200 p-6">
      <div className="flex gap-4">
        {/* Product Image */}
        <div className="flex-shrink-0">
          <img 
            src={productImage} 
            alt={item.product.name} 
            className="w-24 h-24 object-cover rounded" 
          />
        </div>
        
        {/* Product Details */}
        <div className="flex-grow">
          <h3 className="text-lg font-medium text-gray-900 mb-1">{item.product.name}</h3>
          <p className="text-sm text-gray-600 mb-2">{item.product.description?.substring(0, 100)}...</p>
          
          {/* Seller Info */}
          <p className="text-sm text-gray-500 mb-3">
            Seller: <span className="text-blue-600">{item.vendor?.businessName || 'Unknown'}</span>
            <span className="ml-2 text-green-600">✓ Assured</span>
          </p>
          
          {/* Price Information */}
          <div className="flex items-center gap-2 mb-3">
            <span className="text-2xl font-semibold text-gray-900">{getCurrencySymbol()}{itemPrice.toFixed(0)}</span>
            <span className="text-lg text-gray-500 line-through">{getCurrencySymbol()}{originalPrice.toFixed(0)}</span>
            <span className="text-green-600 font-medium">{discount}% off</span>
          </div>
          
          {/* Quantity Controls */}
          <div className="flex items-center gap-4 mb-3">
            <div className="flex items-center border border-gray-300 rounded">
              <button 
                onClick={decrementQuantity} 
                className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" 
                disabled={loading || item.quantity <= 1}
              >
                −
              </button>
              <span className="w-12 h-8 flex items-center justify-center border-x border-gray-300 text-sm font-medium">
                {item.quantity}
              </span>
              <button 
                onClick={incrementQuantity} 
                className="w-8 h-8 flex items-center justify-center hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed" 
                disabled={loading}
              >
                +
              </button>
            </div>
            <span className="text-sm text-gray-600">Or Pay {getCurrencySymbol()}{(itemPrice/9).toFixed(0)} + {getCurrencySymbol()}9</span>
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-4">
            <button 
              onClick={handleRemove}
              className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors disabled:opacity-50" 
              disabled={loading}
            >
              REMOVE
            </button>
          </div>
        </div>
        
        {/* Delivery Info */}
        <div className="flex-shrink-0 text-right">
          <p className="text-sm text-gray-600 mb-1">Delivery by Sun Jul 27</p>
          <p className="text-sm text-green-600">Free delivery</p>
        </div>
      </div>
    </div>
  );
};

// Cart Summary Component
const CartSummary = ({ cart, clearCart, loading }) => {
  const { getCurrencySymbol } = useCurrency();
  if (!cart || cart.items.length === 0) return null;

  const originalTotal = cart.totalAmount; // Use actual cart total
  const savings = 0; // No savings without coupons

  return (
    <div className="bg-white border border-gray-200 sticky top-4">
      {/* Price Details Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-800">PRICE DETAILS</h2>
      </div>
      
      {/* Price Breakdown */}
      <div className="px-6 py-4 space-y-3">
        <div className="flex justify-between items-center">
          <span className="text-gray-700">Price ({cart.totalItems} items)</span>
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10 2L3 7v11h4v-6h6v6h4V7l-7-5z"/>
            </svg>
            <span className="font-medium">{getCurrencySymbol()}{originalTotal.toFixed(0)}</span>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-gray-700">Protect Promise Fee</span>
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
              <circle cx="10" cy="10" r="8"/>
              <path d="M10 6v4l3 3"/>
            </svg>
            <span className="font-medium">{getCurrencySymbol()}28</span>
          </div>
        </div>
        
        <div className="border-t border-gray-200 pt-3 mt-3">
          <div className="flex justify-between items-center text-lg font-semibold">
            <span>Total Amount</span>
            <span>{getCurrencySymbol()}{(cart.totalAmount + 28).toFixed(0)}</span>
          </div>
        </div>
      </div>
      
      {/* Trust Badge */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center text-sm text-gray-600">
          <svg className="w-5 h-5 mr-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <div>
            <div className="font-medium">Safe and Secure Payments. Easy returns.</div>
            <div>100% Authentic products.</div>
          </div>
        </div>
      </div>
      
      {/* Place Order Button */}
      <div className="px-6 py-4">
        <button 
          onClick={() => window.location.href = '/checkout'}
          className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded transition-colors"
        >
          PROCEED TO CHECKOUT
        </button>
      </div>
    </div>
  );
};

const CartPage = () => {
  const { cart, fetchCart, removeFromCart, updateCartItem, clearCart, loading, error } = useCart();

  useEffect(() => {
    // Only fetch cart if it's not already loaded
    if (!cart) {
      fetchCart();
    }
  }, [cart, fetchCart]);

  const hasItems = cart && cart.items && cart.items.length > 0;

  return (
    <div className="min-h-screen bg-gray-100">
      
      {/* Address Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center text-sm text-gray-600">
            <span className="font-medium">Deliver to:</span>
            <span className="ml-2 font-bold text-gray-900">User Name, 201102</span>
            <button className="ml-2 text-blue-600 hover:text-blue-800 transition-colors">
              Change
            </button>
            <span className="ml-4 text-xs bg-gray-100 px-2 py-1 rounded">HOME</span>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            House No 105A, Gali No 2, Khushi Vatika, Tila Shahbazpur, B...
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-6">
        
        {loading && (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        )}
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-600">Error: {error}</p>
          </div>
        )}
        
        {!loading && !hasItems && (
          <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
            <img 
              src="https://i.ibb.co/9mQR1Z1H/Alicartify-Logo.png" 
              alt="Logo" 
              className="w-32 h-32 mx-auto mb-6 object-contain opacity-50"
            />
            <h2 className="text-2xl font-semibold text-gray-800 mb-4">Your cart is empty</h2>
            <p className="text-gray-600 text-lg mb-6">Add some products to get started!</p>
            <a 
              href="/" 
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors"
            >
              Continue Shopping
            </a>
          </div>
        )}
        
        {hasItems && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                {cart.items.map((item, index) => (
                  <CartItem 
                    key={`${item.product._id}-${item.selectedVariant?.sku || 'default'}-${index}`}
                    item={item}
                    removeFromCart={removeFromCart}
                    updateCartItem={updateCartItem}
                    loading={loading}
                  />
                ))}
              </div>
            </div>
            
            {/* Cart Summary */}
            <div>
              <CartSummary 
                cart={cart}
                clearCart={clearCart}
                loading={loading}
              />
            </div>
          </div>
        )}
      </div>
      
      <Footer />
    </div>
  );
};

export default CartPage;
