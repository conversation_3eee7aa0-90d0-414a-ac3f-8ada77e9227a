require('dotenv').config();
const mongoose = require('mongoose');
const Product = require('./src/models/Product');

const testProductStats = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB connected successfully.');

    const totalProducts = await Product.countDocuments();
    const activeProducts = await Product.countDocuments({ status: 'active' });
    const pendingProducts = await Product.countDocuments({ status: 'pending_approval' });
    const lowStockProducts = await Product.countDocuments({ 
      'inventory.quantity': { $lt: 10, $gt: 0 },
      'inventory.trackQuantity': true 
    });

    console.log('--- Product Statistics ---');
    console.log(`Total Products: ${totalProducts}`);
    console.log(`Active Products: ${activeProducts}`);
    console.log(`Pending Approval: ${pendingProducts}`);
    console.log(`Low Stock Products: ${lowStockProducts}`);
    console.log('--------------------------');

  } catch (error) {
    console.error('Error testing product stats:', error);
  } finally {
    mongoose.disconnect();
    console.log('MongoDB disconnected.');
  }
};

testProductStats();
