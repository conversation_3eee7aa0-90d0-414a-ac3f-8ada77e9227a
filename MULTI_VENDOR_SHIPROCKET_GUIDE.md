# Multi-Vendor Shiprocket Integration Guide

## Overview

This system implements **Amazon-style multi-vendor shipping** where each vendor's products are shipped from their own business address as pickup locations. When a customer places an order with items from multiple vendors, the system automatically:

1. **Groups items by vendor**
2. **Creates separate Shiprocket orders** for each vendor
3. **Uses each vendor's business address** as their pickup location
4. **Manages tracking** for each vendor's shipment separately

## 🏗️ Architecture

### Key Components

1. **Enhanced Shiprocket Service** (`shiprocketService.js`)
   - Automatically creates pickup locations from vendor addresses
   - Groups orders by vendor
   - Creates separate shipments for each vendor

2. **Updated Order Model** (`Order.js`)
   - Stores Shiprocket order details for each vendor
   - Tracks multiple AWB codes per order

3. **Smart Order Controller** (`customerOrderController.js`)
   - Processes multi-vendor orders seamlessly
   - Handles vendor-specific shipping logic

## 🚀 How It Works

### 1. Vendor Registration
When vendors register, their business address is stored:
```javascript
businessAddress: {
  street: "123 Business Street",
  city: "Mumbai", 
  state: "Maharashtra",
  zipCode: "400001",
  country: "India"
}
```

### 2. Pickup Location Creation
For each vendor, the system:
- Generates a unique pickup location name: `VendorName_City`
- Creates/finds the pickup location in Shiprocket
- Uses vendor's business address as pickup address

### 3. Order Processing
When a customer places an order:

```javascript
// Original order with mixed vendor items
{
  items: [
    { vendor: "vendor1", product: "Product A", quantity: 2 },
    { vendor: "vendor1", product: "Product B", quantity: 1 },
    { vendor: "vendor2", product: "Product C", quantity: 1 }
  ]
}

// System automatically creates:
// Order 1: ORD123-V1 (Vendor 1 items) → Ships from Vendor 1's address
// Order 2: ORD123-V2 (Vendor 2 items) → Ships from Vendor 2's address
```

## 📋 Environment Variables Required

### Shiprocket Configuration
```env
# Required for Shiprocket integration
SHIPROCKET_EMAIL=<EMAIL>
SHIPROCKET_PASSWORD=your-shiprocket-password

# Optional: Default pickup location fallback
SHIPROCKET_PICKUP_LOCATION=Primary
```

### Razorpay Configuration
```env
# Required for payment processing
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
```

## 🔧 Implementation Details

### Pickup Location Naming Convention
```javascript
// Example: "TechStore_Mumbai" for a vendor named "Tech Store" in Mumbai
generatePickupLocationName(vendor) {
  const locationName = `${vendor.businessName.replace(/[^a-zA-Z0-9\s]/g, '').substring(0, 20)}_${vendor.businessAddress.city}`;
  return locationName.replace(/\s+/g, '_');
}
```

### Vendor Grouping Logic
```javascript
// Groups items by vendor ID
const vendorGroups = {};
for (const item of orderData.items) {
  const vendorId = item.vendor.toString();
  if (!vendorGroups[vendorId]) {
    vendorGroups[vendorId] = { items: [], totalAmount: 0 };
  }
  vendorGroups[vendorId].items.push(item);
  vendorGroups[vendorId].totalAmount += parseFloat(item.totalPrice);
}
```

### Proportional Shipping Calculation
```javascript
// Distributes shipping cost proportionally among vendors
const vendorShippingCharge = (orderData.pricing.shipping || 0) * 
  (vendorGroup.totalAmount / orderData.pricing.subtotal);
```

## 📦 Order Structure

### Single Order → Multiple Shiprocket Orders
```javascript
// Customer Order: ORD241201123456
{
  orderNumber: "ORD241201123456",
  shiprocketOrders: [
    {
      vendorId: "vendor1_id",
      vendorName: "Tech Store",
      shiprocketOrderId: "ORD241201123456-V1",
      awbCode: "AWB123456789",
      courierName: "Delhivery",
      pickupLocation: "TechStore_Mumbai",
      items: [
        { name: "Laptop", quantity: 1, sku: "LAP001" }
      ]
    },
    {
      vendorId: "vendor2_id", 
      vendorName: "Fashion Hub",
      shiprocketOrderId: "ORD241201123456-V2",
      awbCode: "AWB987654321",
      courierName: "BlueDart",
      pickupLocation: "FashionHub_Delhi",
      items: [
        { name: "T-Shirt", quantity: 2, sku: "TSH001" }
      ]
    }
  ]
}
```

## 🧪 Testing

### Run the Test Script
```bash
cd server
node test-vendor-pickup-locations.js
```

This will:
- ✅ Connect to your database
- ✅ Find an active vendor
- ✅ Generate pickup location name
- ✅ Test Shiprocket API (if credentials configured)
- ✅ Simulate multi-vendor order processing

### Expected Output
```
📍 Testing Vendor Pickup Location Creation
==========================================
Vendor: Tech Store
Address: 123 Business St, Mumbai, Maharashtra 400001
Email: <EMAIL>
Phone: 9876543210

🏷️  Generated pickup location name: TechStore_Mumbai

📦 Simulating Multi-Vendor Order Processing
============================================
Order contains 2 items from vendor: Tech Store
Total order value: ₹435
Pickup location would be: TechStore_Mumbai
Vendor address: 123 Business St, Mumbai

✅ Test completed successfully!
```

## 🎯 Benefits

### For Customers
- **Faster delivery** from local vendor locations
- **Accurate tracking** for each vendor's items
- **Transparent shipping** with vendor-specific updates

### For Vendors
- **Ships from their own location** (like Amazon FBA)
- **No central warehouse dependency**
- **Maintains brand identity** in shipping

### For Platform
- **Scalable architecture** supports unlimited vendors
- **Reduced logistics complexity**
- **Better vendor satisfaction**

## 🔍 Monitoring & Tracking

### Order Tracking
Each vendor's shipment gets its own:
- **AWB Code** for tracking
- **Courier assignment**
- **Delivery timeline**
- **Status updates**

### Error Handling
- If one vendor's shipment fails, others continue
- Fallback to default pickup location if vendor location creation fails
- Comprehensive error logging for debugging

## 🚀 Production Deployment

### Shiprocket Setup
1. **Create Shiprocket account** and complete KYC
2. **Add environment variables** to production
3. **Test with sample orders** before going live
4. **Monitor pickup location creation** in Shiprocket dashboard

### Scaling Considerations
- **Pickup locations are cached** (created once per vendor)
- **Parallel processing** of vendor orders
- **Graceful degradation** if Shiprocket API fails

## 📞 Support

### Common Issues
1. **"Pickup location creation failed"**
   - Check vendor address completeness
   - Verify Shiprocket credentials
   - Ensure vendor has valid business details

2. **"Vendor not found"**
   - Ensure vendor is active and verified
   - Check vendor ID in order items

3. **"Shiprocket authentication failed"**
   - Verify SHIPROCKET_EMAIL and SHIPROCKET_PASSWORD
   - Check Shiprocket account status

### Debug Mode
Enable detailed logging by setting:
```env
NODE_ENV=development
```

This will show detailed Shiprocket API calls and responses.

---

## 🎉 Conclusion

This implementation provides a robust, scalable solution for multi-vendor shipping that mirrors Amazon's approach. Each vendor maintains control over their shipping while the platform provides a seamless customer experience.

The system is designed to be:
- **Fault-tolerant**: Continues working even if some vendors fail
- **Scalable**: Supports unlimited vendors
- **Maintainable**: Clean separation of concerns
- **Customer-friendly**: Transparent tracking and delivery