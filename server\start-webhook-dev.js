#!/usr/bin/env node

/**
 * 🚀 Razorpay Webhook Development Setup
 * 
 * This script starts your server and exposes it through ngrok
 * for easy webhook testing with Razorpay
 */

const { spawn, exec } = require('child_process');
const path = require('path');

console.log('🚀 Starting Razorpay Webhook Development Environment...\n');

// Function to start the server
function startServer() {
  return new Promise((resolve, reject) => {
    console.log('📦 Starting Express server...');
    const server = spawn('npm', ['run', 'dev'], { 
      stdio: 'pipe',
      shell: true,
      cwd: __dirname
    });

    let serverReady = false;

    server.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(output);
      
      if (output.includes('Server is running on port') && !serverReady) {
        serverReady = true;
        resolve(server);
      }
    });

    server.stderr.on('data', (data) => {
      console.error('Server Error:', data.toString());
    });

    server.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Server process exited with code ${code}`));
      }
    });

    // Timeout after 10 seconds
    setTimeout(() => {
      if (!serverReady) {
        reject(new Error('Server failed to start within 10 seconds'));
      }
    }, 10000);
  });
}

// Function to start ngrok with enhanced error handling
function startNgrok() {
  return new Promise((resolve, reject) => {
    console.log('🌐 Starting ngrok tunnel...');
    
    const ngrok = spawn('ngrok', ['http', '5000', '--log=stdout'], { 
      stdio: 'pipe',
      shell: true
    });

    let tunnelUrl = '';
    let hasError = false;

    ngrok.stdout.on('data', (data) => {
      const output = data.toString();
      console.log('ngrok:', output.trim());
      
      // Look for tunnel URL in multiple formats
      const urlPatterns = [
        /https:\/\/[a-z0-9-]+\.(ngrok\.io|ngrok-free\.app)/,
        /url=(https:\/\/[a-z0-9-]+\.(ngrok\.io|ngrok-free\.app))/,
        /Forwarding\s+https:\/\/[a-z0-9-]+\.(ngrok\.io|ngrok-free\.app)/
      ];
      
      for (const pattern of urlPatterns) {
        const match = output.match(pattern);
        if (match && !tunnelUrl) {
          tunnelUrl = match[1] || match[0];
          console.log(`\n✅ ngrok tunnel established: ${tunnelUrl}`);
          resolve({ process: ngrok, url: tunnelUrl });
          return;
        }
      }
      
      // Check if tunnel is started (alternative detection)
      if (output.includes('started tunnel') && output.includes('https://') && !tunnelUrl) {
        const match = output.match(/(https:\/\/[^\s]+)/);
        if (match) {
          tunnelUrl = match[1];
          console.log(`\n✅ ngrok tunnel established: ${tunnelUrl}`);
          resolve({ process: ngrok, url: tunnelUrl });
        }
      }
    });

    ngrok.stderr.on('data', (data) => {
      const errorOutput = data.toString();
      console.error('ngrok Error:', errorOutput);
      hasError = true;
      
      if (errorOutput.includes('command not found') || errorOutput.includes('not recognized')) {
        console.log('\n❌ ngrok is not installed or not in PATH');
        console.log('Please install ngrok from: https://ngrok.com/download');
        console.log('Or run: npm install -g @ngrok/ngrok');
        ngrok.kill();
        reject(new Error('ngrok not found'));
      } else if (errorOutput.includes('authentication failed') || errorOutput.includes('ERR_NGROK')) {
        console.log('\n⚠️ ngrok authentication issue detected');
        console.log('To fix this:');
        console.log('1. Sign up at: https://dashboard.ngrok.com/signup');
        console.log('2. Get your auth token: https://dashboard.ngrok.com/get-started/your-authtoken');
        console.log('3. Run: ngrok config add-authtoken YOUR_TOKEN');
        ngrok.kill();
        reject(new Error('ngrok authentication failed'));
      }
    });

    ngrok.on('close', (code) => {
      if (code !== 0 && !tunnelUrl) {
        reject(new Error(`ngrok process exited with code ${code}`));
      }
    });

    // Extended timeout for slower connections
    setTimeout(() => {
      if (!tunnelUrl && !hasError) {
        console.log('\n⏰ ngrok is taking longer than expected...');
        console.log('This might be due to:');
        console.log('- Slow internet connection');
        console.log('- ngrok service issues');
        console.log('- Firewall blocking ngrok');
        ngrok.kill();
        reject(new Error('ngrok failed to establish tunnel within 25 seconds'));
      }
    }, 25000);
  });
}

// Function to display webhook setup instructions
function displayInstructions(ngrokUrl) {
  console.log('\n' + '='.repeat(80));
  console.log('🎉 WEBHOOK DEVELOPMENT ENVIRONMENT READY!');
  console.log('='.repeat(80));
  console.log('\n📍 Your webhook endpoints:');
  console.log(`   Local:  http://localhost:5000/api/payments/razorpay/webhook`);
  console.log(`   Public: ${ngrokUrl}/api/payments/razorpay/webhook`);
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Go to Razorpay Dashboard: https://dashboard.razorpay.com/');
  console.log('2. Navigate to Settings → Webhooks');
  console.log('3. Click "Create Webhook"');
  console.log(`4. Enter this URL: ${ngrokUrl}/api/payments/razorpay/webhook`);
  console.log('5. Select these events:');
  console.log('   ✅ payment.captured');
  console.log('   ✅ payment.failed');
  console.log('   ✅ payment.authorized');
  console.log('   ✅ order.paid');
  console.log('   ✅ refund.created');
  console.log('6. Copy the webhook secret to your .env file');
  
  console.log('\n🧪 Test your webhook:');
  console.log('   Run: npm run test:webhook');
  console.log(`   Or: curl -X POST ${ngrokUrl}/api/payments/razorpay/webhook \\`);
  console.log('        -H "Content-Type: application/json" \\');
  console.log('        -d \'{"event":"payment.captured","payload":{"payment":{"entity":{"id":"test"}}}}\'');
  
  console.log('\n📱 Your API endpoints:');
  console.log(`   Frontend: http://localhost:5173`);
  console.log(`   Backend:  http://localhost:5000`);
  console.log(`   API:      http://localhost:5000/api`);
  console.log(`   Public:   ${ngrokUrl}/api`);
  
  console.log('\n⚠️  Important Notes:');
  console.log('   • Keep this terminal window open');
  console.log('   • The ngrok URL will change each time you restart');
  console.log('   • Update Razorpay webhook URL when URL changes');
  console.log('   • Use HTTPS webhook URL for production');
  
  console.log('\n' + '='.repeat(80));
  console.log('Press Ctrl+C to stop both server and ngrok');
  console.log('='.repeat(80) + '\n');
}

// Main function
async function main() {
  let server = null;
  let ngrok = null;

  try {
    // Start the server first
    server = await startServer();
    
    // Wait a moment for server to fully initialize
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Start ngrok tunnel
    const { process: ngrokProcess, url: ngrokUrl } = await startNgrok();
    ngrok = ngrokProcess;
    
    // Display instructions
    displayInstructions(ngrokUrl);
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n\n🛑 Shutting down...');
      if (server) server.kill();
      if (ngrok) ngrok.kill();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n\n🛑 Shutting down...');
      if (server) server.kill();
      if (ngrok) ngrok.kill();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    
    if (server) server.kill();
    if (ngrok) ngrok.kill();
    
    console.log('\n🔧 Troubleshooting:');
    console.log('• Make sure port 5000 is not in use');
    console.log('• Install ngrok: npm install -g @ngrok/ngrok');
    console.log('• Check your internet connection');
    console.log('• Try running server manually: npm run dev');
    
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the setup
main();
