const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const morgan = require('morgan');
// const rateLimit = require('express-rate-limit'); // Disabled rate limiting
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');
const multer = require('multer');

// Import middleware
const errorHandler = require('./middleware/errorHandler');
const notFound = require('./middleware/notFound');
const { protectVendorRoute, protectAdminRoute } = require('./middleware/routeProtection');

// Initialize Cloudinary
const { initializeCloudinary } = require('./utils/cloudinaryInit');
initializeCloudinary();

// Import routes
const authRoutes = require('./routes/authRoutes');
const adminRoutes = require('./routes/admin');
const vendorRoutes = require('./routes/vendor');
const customerRoutes = require('./routes/customer');
const publicRoutes = require('./routes/public');
const reviewRoutes = require('./routes/reviews');
const orderTrackingRoutes = require('./routes/orderTrackingRoutes');
const sharedRoutes = require('./routes/shared');
const paymentRoutes = require('./routes/paymentRoutes');
const webhookRoutes = require('./routes/webhooks');

// Create Express app
const app = express();

// Set security HTTP headers
app.use(helmet());

// Development logging
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// CORS configuration - must come before rate limiting
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    // In development, allow all localhost origins
    if (process.env.NODE_ENV !== 'production') {
      if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
        return callback(null, true);
      }
    }
    
    // Production allowed origins
    const allowedOrigins = [
      process.env.FRONTEND_URL || 'http://localhost:5173',
      'https://multi-vendor-ecommerce-seven.vercel.app',
      'https://multi-vendor-server-1tb9.onrender.com',
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:5173',
      'http://localhost:4173',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:5173',
      'http://127.0.0.1:4173'
    ];
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
  preflightContinue: false,
  optionsSuccessStatus: 204
};

app.use(cors(corsOptions));

// Additional CORS headers middleware
app.use((req, res, next) => {
  const origin = req.headers.origin;
  
  // Allow localhost origins in development
  if (origin && (origin.includes('localhost') || origin.includes('127.0.0.1'))) {
    res.header('Access-Control-Allow-Origin', origin);
  }
  
  res.header('Access-Control-Allow-Credentials', 'true');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(204).end();
  }
  
  next();
});

// Rate limiting removed to prevent 429 errors with dashboard auto-refresh
// const limiter = rateLimit({ ... }); // Disabled
// app.use('/api', limiter); // Disabled

// Custom middleware to handle text/plain as JSON (for compatibility)
app.use((req, res, next) => {
  if (req.headers['content-type'] === 'text/plain;charset=UTF-8' && req.method !== 'GET') {
    req.headers['content-type'] = 'application/json';
  }
  next();
});

// Body parser, reading data from body into req.body
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Configure multer for handling multipart/form-data
const multerConfig = {
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    fieldSize: 1024 * 1024 // 1MB field size limit
  },
  fileFilter: (req, file, cb) => {
    // Allow common image formats for profile pictures
    const allowedMimes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'), false);
    }
  }
};

// Make multer config available globally
app.locals.multerConfig = multerConfig;

// Helper function to sanitize objects
function sanitizeObject(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }
  
  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    // Remove keys that start with $ or contain dots (MongoDB operators)
    if (key.startsWith('$') || key.includes('.')) {
      console.warn(`Sanitized dangerous key: ${key}`);
      continue;
    }
    
    if (typeof value === 'object' && value !== null) {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

// Data sanitization against NoSQL query injection
app.use((req, res, next) => {
  try {
    // Only sanitize if body exists and is an object
    if (req.body && typeof req.body === 'object' && Object.keys(req.body).length > 0) {
      req.body = sanitizeObject(req.body);
    }
    
    // Sanitize req.query
    if (req.query && typeof req.query === 'object') {
      const sanitizedQuery = sanitizeObject(req.query);
      Object.keys(req.query).forEach(key => delete req.query[key]);
      Object.assign(req.query, sanitizedQuery);
    }
    
    // Sanitize req.params
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }
    
    next();
  } catch (error) {
    console.warn('Sanitization error:', error.message);
    next();
  }
});

// Data sanitization against XSS
app.use((req, res, next) => {
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = xss(req.body[key]);
      }
    });
  }
  next();
});

// Prevent parameter pollution - simple implementation
app.use((req, res, next) => {
  const whitelist = ['price', 'rating', 'category', 'brand', 'sort', 'limit', 'page'];
  
  if (req.query) {
    Object.keys(req.query).forEach(key => {
      if (Array.isArray(req.query[key]) && !whitelist.includes(key)) {
        req.query[key] = req.query[key][req.query[key].length - 1];
      }
    });
  }
  next();
});

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Server is running successfully',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/admin', protectAdminRoute, adminRoutes);
app.use('/api/vendor', protectVendorRoute, vendorRoutes);
app.use('/api/customer', customerRoutes);
app.use('/api/public', publicRoutes);
app.use('/api/shared', sharedRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/order-tracking', orderTrackingRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/webhooks', webhookRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Welcome to Multi-Vendor eCommerce API',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      auth: '/api/auth',
      admin: '/api/admin',
      vendor: '/api/vendor',
      customer: '/api/customer',
      public: '/api/public',
      reviews: '/api/reviews',
      health: '/api/health'
    }
  });
});

// Serve frontend in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../../client/build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../../client/build', 'index.html'));
  });
}

// 404 handler
app.use(notFound);

// Global error handler
app.use(errorHandler);

module.exports = app;