#!/usr/bin/env node

/**
 * API Endpoints Validation Script
 * Tests the complete order flow through API endpoints
 */

const axios = require('axios');
require('dotenv').config();

const BASE_URL = 'http://localhost:5000/api';

class APIEndpointsTest {
  constructor() {
    this.authToken = null;
    this.testData = {
      customer: null,
      product: null,
      order: null
    };
  }

  async init() {
    console.log('🔗 API Endpoints Integration Test');
    console.log('=================================\n');
    
    // Test server connectivity
    try {
      const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
      console.log('✅ Server is running and accessible');
    } catch (error) {
      console.log('❌ Server is not running or not accessible');
      console.log('💡 Please start your server first: npm run dev:simple');
      console.log('💡 Make sure it\'s running on http://localhost:5000');
      return false;
    }
    
    return true;
  }

  async testCustomerAuth() {
    console.log('👤 Testing Customer Authentication');
    console.log('=================================');

    try {
      // Register or login test customer
      const customerData = {
        firstName: 'Test',
        lastName: 'Customer',
        email: '<EMAIL>',
        password: 'testpass123',
        phone: '**********',
        userType: 'customer'
      };

      // Try to login first, if fails then register
      try {
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: customerData.email,
          password: customerData.password
        });
        
        this.authToken = loginResponse.data.data.token;
        console.log('✅ Customer login successful');
      } catch (loginError) {
        // Login failed, try to register
        console.log('🔄 Customer not found, registering new customer...');
        
        const registerResponse = await axios.post(`${BASE_URL}/auth/register`, customerData);
        
        // Now login
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: customerData.email,
          password: customerData.password
        });
        
        this.authToken = loginResponse.data.data.token;
        console.log('✅ Customer registration and login successful');
      }

      console.log(`   Auth Token: ${this.authToken.substring(0, 20)}...`);
      console.log('');
      return true;

    } catch (error) {
      console.error('❌ Customer authentication failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async getTestProduct() {
    console.log('🛍️ Getting Test Product');
    console.log('=======================');

    try {
      // Get products
      const response = await axios.get(`${BASE_URL}/products?limit=1`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (response.data.data.products && response.data.data.products.length > 0) {
        this.testData.product = response.data.data.products[0];
        console.log('✅ Test product found:');
        console.log(`   Name: ${this.testData.product.name}`);
        console.log(`   Price: ₹${this.testData.product.pricing.sellingPrice}`);
        console.log(`   SKU: ${this.testData.product.sku}`);
        console.log('');
        return true;
      } else {
        console.log('❌ No products found in database');
        console.log('💡 Please add some products first through admin panel');
        return false;
      }

    } catch (error) {
      console.error('❌ Failed to get test product:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testRazorpayOrderCreation() {
    console.log('💳 Testing Razorpay Order Creation API');
    console.log('======================================');

    const orderData = {
      items: [
        {
          productId: this.testData.product._id,
          quantity: 2
        }
      ],
      billing: {
        firstName: 'Test',
        lastName: 'Customer',
        email: '<EMAIL>',
        phone: '**********',
        address: {
          street: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001',
          country: 'India'
        }
      },
      shipping: {
        firstName: 'Test',
        lastName: 'Customer',
        address: {
          street: '123 Test Street',
          city: 'Mumbai',
          state: 'Maharashtra',
          zipCode: '400001',
          country: 'India'
        }
      }
    };

    try {
      const response = await axios.post(`${BASE_URL}/payments/razorpay/create-order`, orderData, {
        headers: { 
          Authorization: `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log('✅ Razorpay order created successfully!');
        console.log(`   Order ID: ${response.data.data.order_id}`);
        console.log(`   Razorpay Order ID: ${response.data.data.razorpay_order_id}`);
        console.log(`   Amount: ₹${response.data.data.amount / 100}`);
        console.log(`   Key ID: ${response.data.data.key}`);
        
        this.testData.razorpayOrder = response.data.data;
        console.log('');
        return true;
      } else {
        console.log('❌ Razorpay order creation failed:', response.data.message);
        return false;
      }

    } catch (error) {
      console.error('❌ Razorpay order creation failed:', error.response?.data?.message || error.message);
      
      if (error.response?.data?.message?.includes('Razorpay not configured')) {
        console.log('💡 Please configure your Razorpay credentials in .env file');
      }
      
      return false;
    }
  }

  async testPaymentVerification() {
    console.log('🔒 Testing Payment Verification API');
    console.log('===================================');

    // Simulate payment details (normally these would come from Razorpay frontend)
    const paymentData = {
      razorpay_payment_id: 'pay_test_' + Date.now(),
      razorpay_order_id: this.testData.razorpayOrder?.razorpay_order_id || 'order_test_' + Date.now(),
      razorpay_signature: 'signature_test_' + Date.now(),
      order_id: this.testData.razorpayOrder?.order_id
    };

    try {
      const response = await axios.post(`${BASE_URL}/payments/razorpay/verify`, paymentData, {
        headers: { 
          Authorization: `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.data.success) {
        console.log('✅ Payment verification successful!');
        console.log(`   Order ID: ${response.data.data.orderId}`);
        this.testData.order = { _id: response.data.data.orderId };
        console.log('');
        return true;
      } else {
        console.log('❌ Payment verification failed:', response.data.message);
        return false;
      }

    } catch (error) {
      console.error('❌ Payment verification failed:', error.response?.data?.message || error.message);
      
      if (error.response?.data?.message?.includes('Payment verification failed')) {
        console.log('💡 This is expected in test mode - signature verification will fail');
        console.log('💡 In real scenario, this would work with actual Razorpay payment data');
      }
      
      return false;
    }
  }

  async testOrderStatus() {
    console.log('📦 Testing Order Status API');
    console.log('============================');

    if (!this.testData.order) {
      console.log('⚠️  No order available to check status');
      return true;
    }

    try {
      const response = await axios.get(`${BASE_URL}/payments/status/${this.testData.order._id}`, {
        headers: { Authorization: `Bearer ${this.authToken}` }
      });

      if (response.data.success) {
        console.log('✅ Order status retrieved successfully!');
        console.log(`   Order Number: ${response.data.data.orderNumber}`);
        console.log(`   Payment Status: ${response.data.data.paymentStatus}`);
        console.log(`   Order Status: ${response.data.data.orderStatus}`);
        console.log(`   Total: ₹${response.data.data.total}`);
        console.log('');
        return true;
      } else {
        console.log('❌ Failed to get order status:', response.data.message);
        return false;
      }

    } catch (error) {
      console.error('❌ Order status check failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async testPaymentMethods() {
    console.log('💳 Testing Payment Methods API');
    console.log('===============================');

    try {
      const response = await axios.get(`${BASE_URL}/payments/methods`);

      if (response.data.success) {
        console.log('✅ Payment methods retrieved successfully!');
        response.data.data.methods.forEach(method => {
          console.log(`   ${method.enabled ? '✅' : '❌'} ${method.name}: ${method.description}`);
        });
        console.log('');
        return true;
      } else {
        console.log('❌ Failed to get payment methods:', response.data.message);
        return false;
      }

    } catch (error) {
      console.error('❌ Payment methods check failed:', error.response?.data?.message || error.message);
      return false;
    }
  }

  async generateAPIReport() {
    console.log('📋 API Integration Report');
    console.log('==========================');

    const report = {
      server: { status: 'working' },
      authentication: { status: !!this.authToken ? 'working' : 'failed' },
      products: { status: !!this.testData.product ? 'working' : 'failed' },
      razorpay: { status: !!this.testData.razorpayOrder ? 'working' : 'needs_config' },
      payment: { status: !!this.testData.order ? 'working' : 'test_mode' },
      apis: { status: 'tested' }
    };

    console.log('🔗 API Endpoints Status:');
    console.log(`   Server Connectivity: ${report.server.status === 'working' ? '✅' : '❌'}`);
    console.log(`   Customer Authentication: ${report.authentication.status === 'working' ? '✅' : '❌'}`);
    console.log(`   Product APIs: ${report.products.status === 'working' ? '✅' : '❌'}`);
    console.log(`   Razorpay Integration: ${report.razorpay.status === 'working' ? '✅' : '⚠️'}`);
    console.log(`   Payment Flow: ${report.payment.status === 'working' ? '✅' : '⚠️'}`);

    console.log('\n🎯 Ready for Frontend Integration:');
    const allWorking = report.server.status === 'working' && 
                      report.authentication.status === 'working' && 
                      report.products.status === 'working';
    
    console.log(`   ${allWorking ? '✅' : '❌'} Core APIs: ${allWorking ? 'Working' : 'Need fixes'}`);
    console.log(`   ${report.razorpay.status === 'working' ? '✅' : '⚠️'} Payment APIs: ${report.razorpay.status === 'working' ? 'Working' : 'Need Razorpay config'}`);

    return report;
  }

  async runCompleteTest() {
    try {
      const serverReady = await this.init();
      if (!serverReady) return;

      const authSuccess = await this.testCustomerAuth();
      if (!authSuccess) return;

      const productSuccess = await this.getTestProduct();
      if (!productSuccess) return;

      await this.testPaymentMethods();
      await this.testRazorpayOrderCreation();
      await this.testPaymentVerification();
      await this.testOrderStatus();

      const report = await this.generateAPIReport();

      console.log('\n🎉 API Integration Test Completed!');
      console.log('===================================');

      if (report.server.status === 'working' && report.authentication.status === 'working') {
        console.log('✅ Your server APIs are working correctly!');
        console.log('✅ Frontend can now integrate with these endpoints');
        
        console.log('\n🚀 Frontend Integration Steps:');
        console.log('1. Customer Registration/Login: POST /api/auth/register | /api/auth/login');
        console.log('2. Get Products: GET /api/products');
        console.log('3. Create Razorpay Order: POST /api/payments/razorpay/create-order');
        console.log('4. Show Razorpay Checkout (Frontend handles this)');
        console.log('5. Verify Payment: POST /api/payments/razorpay/verify');
        console.log('6. Check Order Status: GET /api/payments/status/:orderId');
        
        if (report.razorpay.status === 'working') {
          console.log('\n💳 Razorpay Flow:');
          console.log('- ✅ Backend will create Razorpay orders');
          console.log('- ✅ Frontend shows Razorpay checkout window');
          console.log('- ✅ Backend verifies payments');
          console.log('- ✅ Orders appear in Shiprocket dashboard automatically');
        } else {
          console.log('\n⚠️  To enable full Razorpay flow:');
          console.log('- Add real Razorpay credentials to .env file');
          console.log('- RAZORPAY_KEY_ID=rzp_test_your_key_here');
          console.log('- RAZORPAY_KEY_SECRET=your_secret_here');
        }
      }

    } catch (error) {
      console.error('❌ API test failed:', error.message);
    }
  }
}

// Run the test
if (require.main === module) {
  const tester = new APIEndpointsTest();
  tester.runCompleteTest().catch(console.error);
}

module.exports = APIEndpointsTest;
