#!/usr/bin/env node

/**
 * 🔧 Kill all ngrok processes
 * 
 * This utility kills all running ngrok processes
 * Useful when ngrok gets stuck or you need to restart
 */

const { exec } = require('child_process');
const os = require('os');

console.log('🔧 Killing all ngrok processes...\n');

function killNgrokProcesses() {
  const isWindows = os.platform() === 'win32';
  
  if (isWindows) {
    // Windows command to kill ngrok processes
    exec('taskkill /F /IM ngrok.exe', (error, stdout, stderr) => {
      if (error) {
        if (error.message.includes('not found')) {
          console.log('✅ No ngrok processes found running');
        } else {
          console.error('❌ Error:', error.message);
        }
        return;
      }
      
      console.log('✅ ngrok processes terminated successfully');
      console.log(stdout);
    });
  } else {
    // Unix/Linux/Mac command to kill ngrok processes
    exec('pkill -f ngrok', (error, stdout, stderr) => {
      if (error) {
        if (error.code === 1) {
          console.log('✅ No ngrok processes found running');
        } else {
          console.error('❌ Error:', error.message);
        }
        return;
      }
      
      console.log('✅ ngrok processes terminated successfully');
    });
  }
}

killNgrokProcesses();
