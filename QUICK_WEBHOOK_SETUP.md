# 🚀 Quick Razorpay Webhook Setup

## Step 1: Start Development Server with ngrok

```bash
cd server
npm run webhook:dev
```

This will:
- Start your server on port 5000
- Start ngrok tunnel  
- Show your public webhook URL

## Step 2: Get Razorpay Credentials

1. Go to [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Get API Keys: Settings → API Keys
3. Copy Key ID and Key Secret

## Step 3: Update .env File

```env
RAZORPAY_KEY_ID=rzp_test_your_actual_key
RAZORPAY_KEY_SECRET=your_actual_secret
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret
```

## Step 4: Create Webhook in Razorpay

1. Go to Razorpay Dashboard → Settings → Webhooks
2. Click "Create Webhook" 
3. Enter URL: `https://your-ngrok-url.ngrok.io/api/payments/razorpay/webhook`
4. Select events:
   - ✅ payment.captured
   - ✅ payment.failed
5. Copy webhook secret to .env

## Step 5: Test

```bash
npm run test:webhook
```

## For Production

1. Replace `rzp_test_` with `rzp_live_` keys
2. Use your actual domain: `https://yourdomain.com/api/payments/razorpay/webhook`
3. Set `NODE_ENV=production`

That's it! Your webhook is ready. 🎉
