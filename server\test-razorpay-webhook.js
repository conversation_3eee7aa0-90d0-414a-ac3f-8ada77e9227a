const axios = require('axios');
const crypto = require('crypto');

// Test Razorpay Webhook
async function testRazorpayWebhook(customUrl) {
  console.log('🧪 Testing Razorpay Webhook...\n');

  // Your webhook URL (change this to your actual URL)
  const webhookUrl = customUrl || process.env.WEBHOOK_TEST_URL || 'http://localhost:5000/api/payments/razorpay/webhook';
  
  console.log('🎯 Testing webhook endpoint:', webhookUrl);
  
  // Sample webhook payload from Razorpay
  const webhookPayload = {
    entity: 'event',
    account_id: 'acc_BFQ7uQEaa30PBf',
    event: 'payment.captured',
    contains: ['payment'],
    payload: {
      payment: {
        entity: {
          id: 'pay_29QQoUBi66xm2f',
          amount: 50000,
          currency: 'INR',
          status: 'captured',
          order_id: 'order_9A33XWu170gUtm',
          method: 'card',
          captured: true,
          created_at: Math.floor(Date.now() / 1000)
        }
      }
    },
    created_at: Math.floor(Date.now() / 1000)
  };

  try {
    // Generate signature (if webhook secret is configured)
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    let headers = {
      'Content-Type': 'application/json'
    };

    if (webhookSecret) {
      const body = JSON.stringify(webhookPayload);
      const signature = crypto.createHmac('sha256', webhookSecret).update(body).digest('hex');
      headers['x-razorpay-signature'] = signature;
      console.log('✅ Generated webhook signature');
    } else {
      console.log('⚠️  No webhook secret found - signature verification will be skipped');
    }

    console.log('📤 Sending webhook to:', webhookUrl);
    console.log('📦 Payload:', JSON.stringify(webhookPayload, null, 2));

    // Send webhook request
    const response = await axios.post(webhookUrl, webhookPayload, { headers });

    console.log('\n✅ Webhook Response:');
    console.log('Status:', response.status);
    console.log('Data:', response.data);

    if (response.status === 200) {
      console.log('\n🎉 Webhook test successful!');
    }

  } catch (error) {
    console.log('\n❌ Webhook test failed:');
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Data:', error.response.data);
    } else {
      console.log('Error:', error.message);
    }
    
    console.log('\n💡 Troubleshooting tips:');
    console.log('1. Make sure your server is running on port 5000');
    console.log('2. Check if RAZORPAY_WEBHOOK_SECRET is set in .env');
    console.log('3. Verify the webhook endpoint exists');
  }
}

// Run the test
if (require.main === module) {
  require('dotenv').config();
  testRazorpayWebhook().catch(console.error);
}

module.exports = { testRazorpayWebhook };