# Razorpay Webhook Setup Guide

## 🎯 What are Webhooks?

Webhooks are HTTP callbacks that <PERSON><PERSON><PERSON><PERSON> sends to your server when payment events occur (like payment success, failure, etc.). This allows your system to automatically update order status without manual checking.

## 🚀 Step-by-Step Setup

### Step 1: Get Your Webhook URL

Your webhook endpoint is already set up in the code at:
```
https://yourdomain.com/api/payments/razorpay/webhook
```

For local development:
```
http://localhost:5000/api/payments/razorpay/webhook
```

### Step 2: Create Webhook in Razorpay Dashboard

1. **Login to Razorpay Dashboard**
   - Go to [https://dashboard.razorpay.com/](https://dashboard.razorpay.com/)
   - Login with your credentials

2. **Navigate to Webhooks**
   - Go to **Settings** → **Webhooks**
   - Click **"+ Create Webhook"**

3. **Configure Webhook**
   ```
   Webhook URL: https://yourdomain.com/api/payments/razorpay/webhook
   Active Events: Select the following events:
   ✅ payment.authorized
   ✅ payment.captured  
   ✅ payment.failed
   ✅ order.paid
   ✅ refund.created
   ```

4. **Get Webhook Secret**
   - After creating, copy the **Webhook Secret**
   - Add it to your `.env` file:
   ```env
   RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret_here
   ```

### Step 3: For Local Development (Using ngrok)

If testing locally, you need to expose your local server:

1. **Install ngrok**
   ```bash
   npm install -g ngrok
   # or download from https://ngrok.com/
   ```

2. **Expose your local server**
   ```bash
   ngrok http 5000
   ```

3. **Use ngrok URL in Razorpay**
   ```
   Webhook URL: https://abc123.ngrok.io/api/payments/razorpay/webhook
   ```

## 🔧 Environment Variables

Add these to your `.env` file:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## 📋 Webhook Events Handled

Your system will automatically handle these events:

| Event | Description | Action |
|-------|-------------|---------|
| `payment.captured` | Payment successful | Update order status to "paid" |
| `payment.failed` | Payment failed | Update order status to "failed" |
| `payment.authorized` | Payment authorized | Update order status to "authorized" |
| `order.paid` | Order fully paid | Mark order as completed |
| `refund.created` | Refund processed | Update refund status |

## 🧪 Testing Webhooks

### Method 1: Razorpay Test Mode
1. Use test credentials in Razorpay dashboard
2. Make test payments using test cards:
   ```
   Card Number: 4111 1111 1111 1111
   Expiry: Any future date
   CVV: Any 3 digits
   ```

### Method 2: Manual Testing
1. Go to Razorpay Dashboard → Webhooks
2. Find your webhook
3. Click **"Test Webhook"**
4. Send sample events to test your endpoint

## 🔍 Webhook Payload Example

When Razorpay sends a webhook, it looks like this:

```json
{
  "entity": "event",
  "account_id": "acc_BFQ7uQEaa30PBf",
  "event": "payment.captured",
  "contains": ["payment"],
  "payload": {
    "payment": {
      "entity": {
        "id": "pay_29QQoUBi66xm2f",
        "amount": 50000,
        "currency": "INR",
        "status": "captured",
        "order_id": "order_9A33XWu170gUtm",
        "method": "card"
      }
    }
  },
  "created_at": **********
}
```

## 🛠️ Code Implementation

The webhook handler is already implemented in your codebase:

**File:** `server/src/controllers/razorpayController.js`

```javascript
const handleRazorpayWebhook = async (req, res) => {
  try {
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    
    // Verify webhook signature
    if (webhookSecret) {
      const signature = req.headers['x-razorpay-signature'];
      const body = JSON.stringify(req.body);
      const expectedSignature = crypto.createHmac('sha256', webhookSecret)
        .update(body).digest('hex');
      
      if (signature !== expectedSignature) {
        return res.status(400).json({ error: 'Invalid signature' });
      }
    }
    
    const { event, payload } = req.body;
    console.log('Razorpay Webhook:', event);
    
    // Handle different events
    switch (event) {
      case 'payment.captured':
        // Update order status to paid
        break;
      case 'payment.failed':
        // Update order status to failed
        break;
      // ... other events
    }
    
    res.json({ success: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ success: false });
  }
};
```

## 🔒 Security

1. **Webhook Secret Verification**
   - Always verify the webhook signature
   - Use the `RAZORPAY_WEBHOOK_SECRET` for verification

2. **HTTPS Only**
   - Use HTTPS URLs for production webhooks
   - Razorpay requires HTTPS for webhook URLs

3. **Idempotency**
   - Handle duplicate webhook calls gracefully
   - Check if the event was already processed

## 🚨 Troubleshooting

### Common Issues:

1. **Webhook not receiving events**
   - Check if URL is accessible publicly
   - Verify webhook is active in Razorpay dashboard
   - Check server logs for errors

2. **Signature verification failed**
   - Ensure `RAZORPAY_WEBHOOK_SECRET` is correct
   - Check if request body is being parsed correctly

3. **Local development issues**
   - Use ngrok to expose local server
   - Update webhook URL in Razorpay dashboard

### Debug Steps:

1. **Check webhook logs in Razorpay Dashboard**
   - Go to Settings → Webhooks
   - Click on your webhook
   - Check delivery attempts and responses

2. **Add logging to your webhook handler**
   ```javascript
   console.log('Webhook received:', req.body);
   console.log('Headers:', req.headers);
   ```

3. **Test with curl**
   ```bash
   curl -X POST https://yourdomain.com/api/payments/razorpay/webhook \
     -H "Content-Type: application/json" \
     -d '{"event":"payment.captured","payload":{"payment":{"entity":{"id":"test"}}}}'
   ```

## ✅ Verification Checklist

- [ ] Razorpay webhook created in dashboard
- [ ] Webhook URL is publicly accessible
- [ ] `RAZORPAY_WEBHOOK_SECRET` added to `.env`
- [ ] Webhook events selected (payment.captured, payment.failed, etc.)
- [ ] Test webhook with sample payment
- [ ] Check webhook delivery logs in Razorpay dashboard
- [ ] Verify order status updates automatically

## 🎉 You're Done!

Once set up, your system will automatically:
- ✅ Receive payment notifications from Razorpay
- ✅ Update order status in real-time
- ✅ Handle payment failures gracefully
- ✅ Process refunds automatically

Your webhook endpoint: `https://yourdomain.com/api/payments/razorpay/webhook`