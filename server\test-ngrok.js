#!/usr/bin/env node

/**
 * 🧪 Test ngrok Configuration
 * 
 * This script tests if ngrok is properly configured
 */

const { spawn } = require('child_process');

console.log('🧪 Testing ngrok configuration...\n');

function testNgrok() {
  return new Promise((resolve, reject) => {
    console.log('🌐 Starting ngrok test tunnel on port 3000...');
    
    const ngrok = spawn('ngrok', ['http', '3000', '--log=stdout'], { 
      stdio: 'pipe',
      shell: true
    });

    let output = '';
    let hasUrl = false;

    ngrok.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      console.log('ngrok:', text.trim());
      
      // Look for tunnel URL in both stdout formats
      if ((text.includes('https://') && (text.includes('.ngrok.io') || text.includes('.ngrok-free.app'))) ||
          text.includes('started tunnel')) {
        const match = text.match(/https:\/\/[a-z0-9-]+\.(ngrok\.io|ngrok-free\.app)/) || 
                     text.match(/url=(https:\/\/[a-z0-9-]+\.(ngrok\.io|ngrok-free\.app))/);
        if (match && !hasUrl) {
          hasUrl = true;
          const url = match[1] || match[0];
          console.log(`\n✅ ngrok tunnel established successfully!`);
          console.log(`🔗 Tunnel URL: ${url}`);
          console.log('\n🎉 ngrok is working correctly!\n');
          
          // Kill ngrok after successful test
          setTimeout(() => {
            ngrok.kill();
            resolve(url);
          }, 2000);
        }
      }
    });

    ngrok.stderr.on('data', (data) => {
      const errorText = data.toString();
      console.error('ngrok Error:', errorText);
      
      if (errorText.includes('authentication failed') || errorText.includes('ERR_NGROK_4018')) {
        console.log('\n❌ Authentication Error:');
        console.log('1. Sign up at: https://dashboard.ngrok.com/signup');
        console.log('2. Get your auth token: https://dashboard.ngrok.com/get-started/your-authtoken');
        console.log('3. Run: ngrok config add-authtoken YOUR_TOKEN_HERE\n');
        ngrok.kill();
        reject(new Error('Authentication required'));
      }
    });

    ngrok.on('close', (code) => {
      if (code !== 0 && !hasUrl) {
        reject(new Error(`ngrok process exited with code ${code}`));
      } else if (hasUrl) {
        resolve();
      }
    });

    // Timeout after 15 seconds
    setTimeout(() => {
      if (!hasUrl) {
        console.log('⏰ Test timeout - killing ngrok...');
        ngrok.kill();
        reject(new Error('ngrok test timeout'));
      }
    }, 15000);
  });
}

// Run the test
testNgrok()
  .then(() => {
    console.log('✅ ngrok configuration test completed successfully!');
    console.log('🚀 You can now use: npm run webhook:dev');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ ngrok test failed:', error.message);
    console.log('\n🔧 Setup Steps:');
    console.log('1. Sign up: https://dashboard.ngrok.com/signup');
    console.log('2. Get token: https://dashboard.ngrok.com/get-started/your-authtoken');
    console.log('3. Configure: ngrok config add-authtoken YOUR_TOKEN');
    console.log('4. Test again: node test-ngrok.js');
    process.exit(1);
  });
