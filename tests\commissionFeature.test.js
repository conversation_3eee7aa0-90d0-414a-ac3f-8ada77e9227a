const request = require('supertest');
const app = require('../src/app'); // Adjust the path if needed
const mongoose = require('mongoose');

describe('Commission Feature Test', () => {
    let vendorId;
    let orderId;
    let payoutId;
    
    beforeAll(async () => {
        // Connect to test database or mock DB
        mongoose.connect('mongodb://localhost:27017/test_commission', { useNewUrlParser: true, useUnifiedTopology: true });
    });

    afterAll(async () => {
        // Disconnect from test database
        await mongoose.connection.close();
    });

    test('Admin can update vendor commission rate', async () => {
        const response = await request(app)
            .put('/api/admin/vendors/123/commission')
            .send({ rate: 20, type: 'percentage' })
            .expect(200);

        expect(response.body.success).toBe(true);
    });

    test('Vendor commission is applied on order creation', async () => {
        const orderResponse = await request(app)
            .post('/api/customer/orders')
            .send({
                items: [{
                    product: '123',
                    vendor: '456',
                    quantity: 1,
                    unitPrice: 100
                }],
                billing: {...}, // complete billing details
                shipping: {...}, // complete shipping details
                payment: {...}, // complete payment details
                pricing: {...} // complete pricing details
            })
            .expect(201);

        orderId = orderResponse.body.data.order._id;
        expect(orderResponse.body.success).toBe(true);
        expect(orderResponse.body.data.order.items[0].vendorCommission).toBe(15); // Ensure correct commission
    });

    test('Vendor can request payout if balance is sufficient', async () => {
        const payoutResponse = await request(app)
            .post('/api/vendor/payouts')
            .send({ amount: 50, method: 'paypal', paypalEmail: '<EMAIL>' })
            .expect(201);

        payoutId = payoutResponse.body.data.payoutId;
        expect(payoutResponse.body.success).toBe(true);
    });

    test('Admin can approve the payout request', async () => {
        const approvalResponse = await request(app)
            .post(`/api/admin/payouts/${payoutId}/approve`)
            .send({ notes: 'Approved by Admin' })
            .expect(200);

        expect(approvalResponse.body.success).toBe(true);
    });

    test('Order status updates affecting commission and payouts', async () => {
        const statusResponse = await request(app)
            .put(`/api/vendor/orders/${orderId}/status`)
            .send({ status: 'shipped', itemIds: [] })
            .expect(200);

        expect(statusResponse.body.success).toBe(true);
        const orderDetails = await request(app)
            .get(`/api/vendor/orders/${orderId}`)
            .expect(200);

        expect(orderDetails.body.data.vendorItems[0].status).toBe('shipped');
    });
});

