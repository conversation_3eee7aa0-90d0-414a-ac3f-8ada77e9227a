const Razorpay = require('razorpay');
const crypto = require('crypto');
const { Order, Product, Setting } = require('../models');

// Create Razorpay Order
const createRazorpayOrder = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { items, billing, shipping } = req.body;

    // Get Razorpay keys
    const keyId = process.env.RAZORPAY_KEY_ID || (await Setting.findOne({ key: 'razorpay_key_id' }))?.value;
    const keySecret = process.env.RAZORPAY_KEY_SECRET || (await Setting.findOne({ key: 'razorpay_key_secret' }))?.value;
    
    if (!keyId || !keySecret) {
      return res.status(400).json({ success: false, message: 'Razorpay not configured' });
    }

    const razorpay = new Razorpay({ key_id: keyId, key_secret: keySecret });

    // Calculate total
    let total = 0;
    const orderItems = [];

    for (const item of items) {
      const product = await Product.findById(item.productId).populate('vendor');
      if (!product) {
        return res.status(404).json({ success: false, message: 'Product not found' });
      }

      const itemTotal = product.pricing.sellingPrice * item.quantity;
      total += itemTotal;

      orderItems.push({
        product: product._id,
        vendor: product.vendor._id,
        name: product.name,
        sku: product.sku,
        quantity: item.quantity,
        unitPrice: product.pricing.sellingPrice,
        totalPrice: itemTotal,
        status: 'pending'
      });
    }

    // Add shipping and tax
    const shipping_cost = total > 500 ? 0 : 50;
    const tax = Math.round(total * 0.18);
    total = total + shipping_cost + tax;

    const orderNumber = `ORD${Date.now()}`;

    // Create order
    const order = new Order({
      orderNumber,
      customer: userId,
      items: orderItems,
      billing,
      shipping: { ...shipping, cost: shipping_cost },
      payment: { method: 'razorpay', status: 'pending', currency: 'INR' },
      pricing: { subtotal: total - shipping_cost - tax, tax, shipping: shipping_cost, total },
      orderStatus: 'pending'
    });

    await order.save();

    // Create Razorpay order
    const razorpayOrder = await razorpay.orders.create({
      amount: total * 100,
      currency: 'INR',
      receipt: orderNumber,
      notes: {
        order_id: order._id.toString()
      }
    });

    res.json({
      success: true,
      data: {
        order_id: order._id,
        razorpay_order_id: razorpayOrder.id,
        amount: razorpayOrder.amount,
        key: keyId
      }
    });

  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({ success: false, message: 'Failed to create order' });
  }
};

// Verify Razorpay Payment
const verifyRazorpayPayment = async (req, res) => {
  try {
    const { razorpay_payment_id, razorpay_order_id, razorpay_signature, order_id } = req.body;

    const keySecret = process.env.RAZORPAY_KEY_SECRET || (await Setting.findOne({ key: 'razorpay_key_secret' }))?.value;

    // Verify signature
    const body = razorpay_order_id + '|' + razorpay_payment_id;
    const expectedSignature = crypto.createHmac('sha256', keySecret).update(body).digest('hex');

    if (expectedSignature !== razorpay_signature) {
      return res.status(400).json({ success: false, message: 'Payment verification failed' });
    }

    // Update order
    const order = await Order.findById(order_id);
    if (!order) {
      return res.status(404).json({ success: false, message: 'Order not found' });
    }

    // Mark payment as completed
    order.payment.status = 'completed';
    order.payment.transactionId = razorpay_payment_id;
    order.payment.paidAt = new Date();
    order.orderStatus = 'confirmed';
    
    order.items.forEach(item => item.status = 'confirmed');
    await order.save();

    // Update inventory
    for (const item of order.items) {
      await Product.findByIdAndUpdate(item.product, {
        $inc: {
          'inventory.quantity': -item.quantity,
          'sales.totalSold': item.quantity,
          'sales.totalRevenue': item.totalPrice
        }
      });
    }

    // Calculate commissions
    await order.calculateCommissions();

    res.json({ success: true, message: 'Payment verified', data: { orderId: order._id } });

  } catch (error) {
    console.error('Verify payment error:', error);
    res.status(500).json({ success: false, message: 'Payment verification failed' });
  }
};

/**
 * Razorpay Webhook Handler
 */
const handleRazorpayWebhook = async (req, res) => {
  try {
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET;
    
    console.log('📥 Webhook received:', {
      event: req.body.event,
      hasSecret: !!webhookSecret,
      hasSignature: !!req.headers['x-razorpay-signature']
    });
    
    // Verify webhook signature if secret provided
    if (webhookSecret && webhookSecret !== 'whsec_your_webhook_secret_here') {
      const signature = req.headers['x-razorpay-signature'];
      if (!signature) {
        console.log('⚠️  No signature provided in webhook');
        return res.status(400).json({ success: false, message: 'No signature provided' });
      }
      
      const body = JSON.stringify(req.body);
      const expectedSignature = crypto.createHmac('sha256', webhookSecret).update(body).digest('hex');
      
      if (signature !== expectedSignature) {
        console.log('❌ Signature verification failed');
        return res.status(400).json({ success: false, message: 'Invalid signature' });
      }
      console.log('✅ Signature verified successfully');
    } else {
      console.log('⚠️  Webhook secret not configured - skipping signature verification (development mode)');
    }

    const { event, payload } = req.body;
    console.log('Razorpay Webhook:', event);

    // Handle payment events
    if (event === 'payment.captured') {
      const payment = payload.payment.entity;
      const orderId = payment.notes?.order_id;
      
      if (orderId) {
        const order = await Order.findById(orderId);
        if (order && order.payment.status === 'pending') {
          order.payment.status = 'completed';
          order.orderStatus = 'confirmed';
          order.payment.paidAt = new Date();
          await order.save();
        }
      }
    }

    res.json({ success: true });

  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ success: false });
  }
};

/**
 * Get Payment Status
 */
const getPaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId).select('payment orderStatus orderNumber pricing.total');
    
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      data: {
        orderNumber: order.orderNumber,
        paymentStatus: order.payment.status,
        orderStatus: order.orderStatus,
        total: order.pricing.total
      }
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get payment status'
    });
  }
};

module.exports = {
  createRazorpayOrder,
  verifyRazorpayPayment,
  handleRazorpayWebhook,
  getPaymentStatus
};
