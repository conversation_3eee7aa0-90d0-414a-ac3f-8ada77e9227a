const mongoose = require('mongoose');
const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5000'; // Updated to match server port
const TEST_DB = 'mongodb://localhost:27017/test_commission_db';

// Test data
const testData = {
    admin: {
        email: '<EMAIL>',
        password: 'password@admin123'
    },
    vendors: [
        {
            id: null,
            businessName: 'Test Vendor 1',
            commissionRate: 15,
            user: {
                email: '<EMAIL>',
                password: 'Free@009'
            }
        },
        {
            id: null,
            businessName: 'Test Vendor 2',
            commissionRate: 20,
            user: {
                firstName: 'Jane',
                lastName: 'Vendor',
                email: '<EMAIL>',
                password: 'vendor123'
            }
        }
    ],
    customer: {
        firstName: 'Test',
        lastName: 'Customer',
        email: '<EMAIL>',
        password: 'Free@009'
    },
    products: [
        {
            name: 'Test Product 1',
            price: 100,
            vendor: null // Will be set during test
        },
        {
            name: 'Test Product 2',
            price: 200,
            vendor: null // Will be set during test
        }
    ]
};

let authTokens = {
    admin: null,
    vendor1: null,
    vendor2: null,
    customer: null
};

// Utility functions
const makeRequest = async (method, endpoint, data = null, token = null) => {
    const config = {
        method,
        url: `${BASE_URL}/api${endpoint}`,
        headers: {
            'Content-Type': 'application/json',
            ...(token && { 'Authorization': `Bearer ${token}` })
        },
        ...(data && { data })
    };

    try {
        const response = await axios(config);
        return { success: true, data: response.data };
    } catch (error) {
        return { 
            success: false, 
            error: error.response?.data || error.message,
            status: error.response?.status
        };
    }
};

// Test functions
const runCommissionTests = async () => {
    console.log('🚀 Starting Commission Feature Tests...\n');
    
    try {
        // Test 1: Setup and Authentication
        console.log('📝 Test 1: Authentication Setup');
        await setupTestData();
        
        // Test 2: Vendor Commission Rates
        console.log('\n📝 Test 2: Vendor Commission Rate Management');
        await testCommissionRateManagement();
        
        // Test 3: Order Creation with Commission Calculation
        console.log('\n📝 Test 3: Order Creation with Commission Calculation');
        await testOrderCommissionCalculation();
        
        // Test 4: Commission Earning Updates
        console.log('\n📝 Test 4: Commission Earning Updates on Order Status Changes');
        await testCommissionEarningUpdates();
        
        // Test 5: Payout Requests
        console.log('\n📝 Test 5: Vendor Payout Requests');
        await testPayoutRequests();
        
        // Test 6: Admin Payout Processing
        console.log('\n📝 Test 6: Admin Payout Processing');
        await testPayoutProcessing();
        
        // Test 7: Commission Rate Changes Impact
        console.log('\n📝 Test 7: Commission Rate Changes Impact on New Orders');
        await testCommissionRateChangesImpact();
        
        // Test 8: Multi-vendor Order Commission Distribution
        console.log('\n📝 Test 8: Multi-vendor Order Commission Distribution');
        await testMultiVendorCommissionDistribution();
        
        console.log('\n✅ All Commission Feature Tests Completed Successfully!');
        
    } catch (error) {
        console.error('\n❌ Test Suite Failed:', error.message);
        process.exit(1);
    }
};

const setupTestData = async () => {
    // First try to create admin user
    console.log('   🔄 Creating/checking admin user...');
    const adminReg = await makeRequest('POST', '/auth/register', {
        firstName: 'Test',
        lastName: 'Admin',
        email: testData.admin.email,
        password: testData.admin.password,
        role: 'admin'
    });
    
    if (adminReg.success) {
        console.log('   ✅ Admin user created');
    } else {
        console.log('   ℹ️ Admin user may already exist');
    }
    
    // Login as admin
    const adminLogin = await makeRequest('POST', '/auth/login', {
        email: testData.admin.email,
        password: testData.admin.password
    });
    
    if (!adminLogin.success) {
        // Try with default admin credentials
        console.log('   🔄 Trying with default admin credentials...');
        const defaultAdminLogin = await makeRequest('POST', '/auth/login', {
            email: '<EMAIL>',
            password: 'admin123'
        });
        
        if (defaultAdminLogin.success) {
            authTokens.admin = defaultAdminLogin.data.token;
            console.log('   ✅ Admin authenticated with default credentials');
        } else {
            throw new Error('Admin login failed with both test and default credentials');
        }
    } else {
        authTokens.admin = adminLogin.data.token;
        console.log('   ✅ Admin authenticated');
    }
    
    // Login existing vendor
    const vendorLogin = await makeRequest('POST', '/auth/login', {
        email: testData.vendors[0].user.email,
        password: testData.vendors[0].user.password
    });
    
    if (vendorLogin.success) {
        authTokens.vendor1 = vendorLogin.data.token;
        console.log('   📝 Vendor login response:', JSON.stringify(vendorLogin.data, null, 2));
        
        // Get user ID first, then fetch vendor record
        const userId = vendorLogin.data.user.id;
        
        // Fetch vendor record using admin token
        const vendorList = await makeRequest('GET', '/admin/vendors', null, authTokens.admin);
        if (vendorList.success) {
            const vendorRecord = vendorList.data.vendors.find(v => v.user._id === userId || v.user.id === userId);
            if (vendorRecord) {
                testData.vendors[0].id = vendorRecord._id;
                console.log('   ✅ Vendor 1 authenticated');
                console.log('   📝 Vendor ID:', testData.vendors[0].id);
            } else {
                console.log('   ❌ Could not find vendor record for user:', userId);
            }
        } else {
            console.log('   ❌ Failed to fetch vendor list:', vendorList.error);
        }
    } else {
        console.log('   ❌ Vendor authentication failed:', vendorLogin.error);
    }
    
    // Register and authenticate customer
    const customerReg = await makeRequest('POST', '/auth/register', testData.customer);
    if (customerReg.success) {
        const customerLogin = await makeRequest('POST', '/auth/login', {
            email: testData.customer.email,
            password: testData.customer.password
        });
        
        if (customerLogin.success) {
            authTokens.customer = customerLogin.data.token;
            console.log('   ✅ Customer authenticated');
        }
    }
};

const testCommissionRateManagement = async () => {
    // Test updating vendor commission rates (only first vendor for simplicity)
    const vendor = testData.vendors[0];
    
    if (!vendor.id) {
        console.log('   ❌ No vendor ID available for testing');
        return;
    }
    
    const updateCommission = await makeRequest('PUT', `/admin/vendors/${vendor.id}/commission`, {
        rate: vendor.commissionRate,
        type: 'percentage'
    }, authTokens.admin);
    
    if (updateCommission.success) {
        console.log(`   ✅ Updated commission rate for ${vendor.businessName}: ${vendor.commissionRate}%`);
    } else {
        console.log(`   ❌ Failed to update commission for ${vendor.businessName}:`, updateCommission.error);
    }
    
    // Verify the commission rate was updated
    const vendorDetails = await makeRequest('GET', `/admin/vendors/${vendor.id}`, null, authTokens.admin);
    if (vendorDetails.success && vendorDetails.data.data.vendor.commission.rate === vendor.commissionRate) {
        console.log(`   ✅ Commission rate verified for ${vendor.businessName}`);
    } else {
        console.log(`   ❌ Commission rate verification failed for ${vendor.businessName}`);
    }
};

const testOrderCommissionCalculation = async () => {
    // Create products for vendors first
    for (let i = 0; i < testData.products.length; i++) {
        const product = testData.products[i];
        product.vendor = testData.vendors[i].id;
        
        const productCreate = await makeRequest('POST', '/vendor/products', {
            name: product.name,
            pricing: { basePrice: product.price },
            description: 'Test product for commission testing',
            category: 'test-category',
            inventory: { quantity: 100 }
        }, authTokens[`vendor${i + 1}`]);
        
        if (productCreate.success) {
            product.id = productCreate.data.data.product._id;
            console.log(`   ✅ Created product: ${product.name}`);
        }
    }
    
    // Create order with items from different vendors
    const orderData = {
        items: testData.products.map(product => ({
            product: product.id,
            vendor: product.vendor,
            name: product.name,
            quantity: 1,
            unitPrice: product.price,
            totalPrice: product.price
        })),
        billing: {
            firstName: 'Test',
            lastName: 'Customer',
            email: '<EMAIL>',
            phone: '1234567890',
            address: {
                street: '123 Test St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'US'
            }
        },
        shipping: {
            firstName: 'Test',
            lastName: 'Customer',
            address: {
                street: '123 Test St',
                city: 'Test City',
                state: 'Test State',
                zipCode: '12345',
                country: 'US'
            }
        },
        payment: {
            method: 'credit_card'
        },
        pricing: {
            subtotal: 300,
            total: 300
        }
    };
    
    const orderCreate = await makeRequest('POST', '/customer/orders', orderData, authTokens.customer);
    
    if (orderCreate.success) {
        const order = orderCreate.data.data.order;
        console.log(`   ✅ Order created: ${order.orderNumber}`);
        
        // Verify commission calculations
        order.items.forEach((item, index) => {
            const expectedCommission = item.totalPrice * (testData.vendors[index].commissionRate / 100);
            const actualCommission = item.platformCommission || (item.totalPrice * 0.05); // Default 5% if not calculated
            
            console.log(`   📊 Item ${index + 1}: Price=$${item.totalPrice}, Expected Commission=$${expectedCommission.toFixed(2)}, Actual Commission=$${actualCommission.toFixed(2)}`);
        });
        
        // Store order ID for future tests
        testData.orderId = order._id;
    } else {
        console.log('   ❌ Order creation failed:', orderCreate.error);
    }
};

const testCommissionEarningUpdates = async () => {
    if (!testData.orderId) {
        console.log('   ❌ No order ID available for testing');
        return;
    }
    
    // Update order status to delivered to trigger commission earning
    const statusUpdate = await makeRequest('PUT', `/vendor/orders/${testData.orderId}/status`, {
        status: 'delivered',
        note: 'Order delivered - testing commission update'
    }, authTokens.vendor1);
    
    if (statusUpdate.success) {
        console.log('   ✅ Order status updated to delivered');
        
        // Check vendor's commission earnings
        const vendorDetails = await makeRequest('GET', `/admin/vendors/${testData.vendors[0].id}`, null, authTokens.admin);
        
        if (vendorDetails.success) {
            const earnings = vendorDetails.data.data.vendor.commission;
            console.log(`   📊 Vendor earnings - Total: $${earnings.totalEarned}, Pending: $${earnings.pendingAmount}`);
        }
    } else {
        console.log('   ❌ Failed to update order status:', statusUpdate.error);
    }
};

const testPayoutRequests = async () => {
    // Test payout request from vendor
    const payoutRequest = await makeRequest('POST', '/vendor/payouts', {
        amount: 50,
        method: 'paypal',
        paypalEmail: '<EMAIL>'
    }, authTokens.vendor1);
    
    if (payoutRequest.success) {
        console.log('   ✅ Payout request created');
        testData.payoutId = payoutRequest.data.data.payoutId;
        
        // Verify payout appears in vendor's payout list
        const payoutList = await makeRequest('GET', '/vendor/payouts', null, authTokens.vendor1);
        if (payoutList.success && payoutList.data.data.payouts.length > 0) {
            console.log('   ✅ Payout request appears in vendor\'s list');
        }
    } else {
        console.log('   ❌ Payout request failed:', payoutRequest.error);
    }
    
    // Test insufficient balance scenario
    const insufficientPayout = await makeRequest('POST', '/vendor/payouts', {
        amount: 10000, // Large amount
        method: 'paypal',
        paypalEmail: '<EMAIL>'
    }, authTokens.vendor2);
    
    if (!insufficientPayout.success && insufficientPayout.status === 400) {
        console.log('   ✅ Insufficient balance check working correctly');
    } else {
        console.log('   ❌ Insufficient balance check failed');
    }
};

const testPayoutProcessing = async () => {
    if (!testData.payoutId) {
        console.log('   ❌ No payout ID available for testing');
        return;
    }
    
    // Test admin approval
    const approveResponse = await makeRequest('POST', `/admin/payouts/${testData.payoutId}/approve`, {
        notes: 'Approved for testing'
    }, authTokens.admin);
    
    if (approveResponse.success) {
        console.log('   ✅ Payout approved by admin');
        
        // Test completing the payout
        const completeResponse = await makeRequest('POST', `/admin/payouts/${testData.payoutId}/complete`, {
            transactionId: 'TEST_TXN_' + Date.now()
        }, authTokens.admin);
        
        if (completeResponse.success) {
            console.log('   ✅ Payout completed');
            
            // Verify vendor's balance updated
            const vendorDetails = await makeRequest('GET', `/admin/vendors/${testData.vendors[0].id}`, null, authTokens.admin);
            if (vendorDetails.success) {
                const commission = vendorDetails.data.data.vendor.commission;
                console.log(`   📊 Updated vendor balance - Total Paid: $${commission.totalPaid}, Pending: $${commission.pendingAmount}`);
            }
        } else {
            console.log('   ❌ Payout completion failed:', completeResponse.error);
        }
    } else {
        console.log('   ❌ Payout approval failed:', approveResponse.error);
    }
};

const testCommissionRateChangesImpact = async () => {
    // Change commission rate for vendor 1
    const newRate = 25;
    const updateResponse = await makeRequest('PUT', `/admin/vendors/${testData.vendors[0].id}/commission`, {
        rate: newRate,
        type: 'percentage'
    }, authTokens.admin);
    
    if (updateResponse.success) {
        console.log(`   ✅ Commission rate updated to ${newRate}%`);
        
        // Create new order to test new rate
        const newOrderData = {
            items: [{
                product: testData.products[0].id,
                vendor: testData.vendors[0].id,
                name: testData.products[0].name,
                quantity: 1,
                unitPrice: testData.products[0].price,
                totalPrice: testData.products[0].price
            }],
            billing: testData.customer,
            shipping: testData.customer,
            payment: { method: 'credit_card' },
            pricing: { subtotal: testData.products[0].price, total: testData.products[0].price }
        };
        
        const newOrder = await makeRequest('POST', '/customer/orders', newOrderData, authTokens.customer);
        
        if (newOrder.success) {
            const expectedCommission = testData.products[0].price * (newRate / 100);
            console.log(`   📊 New order with updated rate - Expected commission: $${expectedCommission.toFixed(2)}`);
            console.log('   ✅ Commission rate change impact verified');
        }
    } else {
        console.log('   ❌ Commission rate update failed:', updateResponse.error);
    }
};

const testMultiVendorCommissionDistribution = async () => {
    // Create order with items from multiple vendors
    const multiVendorOrderData = {
        items: testData.products.map((product, index) => ({
            product: product.id,
            vendor: testData.vendors[index].id,
            name: product.name,
            quantity: 2,
            unitPrice: product.price,
            totalPrice: product.price * 2
        })),
        billing: testData.customer,
        shipping: testData.customer,
        payment: { method: 'credit_card' },
        pricing: { 
            subtotal: testData.products.reduce((sum, p) => sum + (p.price * 2), 0),
            total: testData.products.reduce((sum, p) => sum + (p.price * 2), 0)
        }
    };
    
    const multiVendorOrder = await makeRequest('POST', '/customer/orders', multiVendorOrderData, authTokens.customer);
    
    if (multiVendorOrder.success) {
        console.log('   ✅ Multi-vendor order created');
        
        // Verify each vendor's commission is calculated correctly
        multiVendorOrder.data.data.order.items.forEach((item, index) => {
            const vendor = testData.vendors[index];
            const expectedCommission = item.totalPrice * (vendor.commissionRate / 100);
            console.log(`   📊 ${vendor.businessName}: Item Total=$${item.totalPrice}, Expected Commission=$${expectedCommission.toFixed(2)}`);
        });
        
        console.log('   ✅ Multi-vendor commission distribution verified');
    } else {
        console.log('   ❌ Multi-vendor order creation failed:', multiVendorOrder.error);
    }
};

// Database cleanup function
const cleanupTestData = async () => {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
        await mongoose.connect(TEST_DB);
        
        // Drop test collections
        const collections = await mongoose.connection.db.collections();
        for (const collection of collections) {
            await collection.drop();
        }
        
        await mongoose.connection.close();
        console.log('   ✅ Test data cleaned up');
    } catch (error) {
        console.log('   ⚠️ Cleanup error (may be expected):', error.message);
    }
};

// Main execution
const main = async () => {
    try {
        await runCommissionTests();
        await cleanupTestData();
    } catch (error) {
        console.error('Test execution failed:', error);
        process.exit(1);
    }
};

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n⚠️ Test interrupted. Cleaning up...');
    await cleanupTestData();
    process.exit(0);
});

// Run tests if script is executed directly
if (require.main === module) {
    main();
}

module.exports = {
    runCommissionTests,
    cleanupTestData
};
