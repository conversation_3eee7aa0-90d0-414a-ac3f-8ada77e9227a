# 🚀 Complete Razorpay Webhook Setup with ngrok

This guide will help you set up Razorpay webhooks for local development using ngrok.

## 🎯 Quick Start (Recommended)

### Step 1: Start the Webhook Development Environment
```bash
cd server
npm run webhook:dev
```

This single command will:
- ✅ Start your Express server
- ✅ Start ngrok tunnel 
- ✅ Display your public webhook URL
- ✅ Show setup instructions

### Step 2: Configure Razorpay Dashboard

1. **Go to [Razorpay Dashboard](https://dashboard.razorpay.com/)**
2. **Navigate to Settings → Webhooks**
3. **Click "Create Webhook"**
4. **Use the ngrok URL shown in your terminal**
   ```
   Example: https://abc123.ngrok.io/api/payments/razorpay/webhook
   ```
5. **Select these events:**
   - ✅ payment.captured
   - ✅ payment.failed  
   - ✅ payment.authorized
   - ✅ order.paid
   - ✅ refund.created

6. **Copy the webhook secret**
7. **Update your `.env` file:**
   ```env
   RAZORPAY_KEY_ID=rzp_test_your_actual_key_here
   RAZORPAY_KEY_SECRET=your_actual_secret_here  
   RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret_here
   ```

### Step 3: Test Your Webhook
```bash
# In a new terminal window
cd server
npm run test:webhook
```

---

## 🔧 Manual Setup (Alternative)

### 1. Install ngrok
```bash
npm install -g @ngrok/ngrok
```

### 2. Start Your Server
```bash
cd server
npm run dev
```

### 3. In Another Terminal, Start ngrok
```bash
ngrok http 5000
```

### 4. Use the ngrok URL
Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`) and use it in Razorpay dashboard:
```
https://abc123.ngrok.io/api/payments/razorpay/webhook
```

---

## 📋 Environment Configuration

### Required .env Variables
```env
# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_your_key_id_here
RAZORPAY_KEY_SECRET=your_razorpay_secret_here
RAZORPAY_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Server Configuration  
PORT=5000
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

### Where to Find Razorpay Credentials

1. **Key ID & Secret:**
   - Go to Razorpay Dashboard → Settings → API Keys
   - Generate test keys if you don't have them

2. **Webhook Secret:**
   - Go to Razorpay Dashboard → Settings → Webhooks
   - After creating webhook, the secret will be shown

---

## 🧪 Testing Your Setup

### Test 1: Basic Webhook Test
```bash
npm run test:webhook
```

### Test 2: Manual curl Test
```bash
curl -X POST http://localhost:5000/api/payments/razorpay/webhook \
  -H "Content-Type: application/json" \
  -d '{"event":"payment.captured","payload":{"payment":{"entity":{"id":"test"}}}}'
```

### Test 3: Test with ngrok URL
```bash
curl -X POST https://your-ngrok-url.ngrok.io/api/payments/razorpay/webhook \
  -H "Content-Type: application/json" \
  -d '{"event":"payment.captured","payload":{"payment":{"entity":{"id":"test"}}}}'
```

---

## 📱 Available Endpoints

### Local Development
- **Frontend:** `http://localhost:5173`
- **Backend:** `http://localhost:5000`
- **Webhook:** `http://localhost:5000/api/payments/razorpay/webhook`

### Public (via ngrok)
- **Backend:** `https://your-ngrok-id.ngrok.io`  
- **Webhook:** `https://your-ngrok-id.ngrok.io/api/payments/razorpay/webhook`
- **All APIs:** `https://your-ngrok-id.ngrok.io/api/*`

---

## 🔍 Webhook Events Handled

| Event | Description | Action |
|-------|-------------|---------|
| `payment.captured` | Payment successful | Update order to "paid" |
| `payment.failed` | Payment failed | Update order to "failed" |  
| `payment.authorized` | Payment authorized | Update order to "authorized" |
| `order.paid` | Order fully paid | Mark order as completed |
| `refund.created` | Refund processed | Update refund status |

---

## 🚨 Troubleshooting

### Issue: Webhook not receiving events
```bash
# Check if your server is running
curl http://localhost:5000/api/health

# Check if webhook endpoint responds
curl -X POST http://localhost:5000/api/payments/razorpay/webhook \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'
```

### Issue: ngrok command not found
```bash
# Install ngrok
npm install -g @ngrok/ngrok

# Or download from https://ngrok.com/download
# Add to your system PATH
```

### Issue: Signature verification failed
- Check that `RAZORPAY_WEBHOOK_SECRET` in `.env` matches the secret from Razorpay dashboard
- Ensure the webhook secret starts with `whsec_`

### Issue: Port 5000 already in use
```bash
# Find what's using port 5000
netstat -ano | findstr :5000

# Kill the process or use a different port
# Update PORT in .env file
```

---

## ⚡ Advanced Usage

### Custom Port
```bash
# Change port in .env
PORT=3001

# Then start ngrok with the new port
ngrok http 3001
```

### Multiple Environments
```bash
# Development
npm run webhook:dev

# Staging  
WEBHOOK_TEST_URL=https://your-staging-url.com/api/payments/razorpay/webhook npm run test:webhook

# Production testing
WEBHOOK_TEST_URL=https://your-production-url.com/api/payments/razorpay/webhook npm run test:webhook
```

---

## 📚 Useful Commands

### Development
```bash
npm run webhook:dev        # Start server + ngrok
npm run dev               # Start server only  
npm run test:webhook      # Test webhook
```

### Production
```bash
npm start                 # Start production server
```

### Debugging
```bash
# Enable debug logging
DEBUG=* npm run dev

# Check database connection
npm run test:db

# Check all connections
npm run test:connections
```

---

## ✅ Verification Checklist

- [ ] ngrok installed and working
- [ ] Server starts without errors
- [ ] ngrok tunnel established  
- [ ] Razorpay webhook created with correct URL
- [ ] Webhook secret copied to `.env`
- [ ] Test webhook returns success
- [ ] Payment events update order status
- [ ] Webhook delivery logs show success in Razorpay dashboard

---

## 🎉 You're All Set!

Your Razorpay webhook integration is now ready for development testing. The webhook will automatically handle payment events and update your order statuses in real-time.

### Next Steps:
1. Create test payments using Razorpay test cards
2. Verify webhook events in Razorpay dashboard
3. Check order status updates in your application
4. Move to production webhooks when ready

### Test Cards for Development:
```
Card Number: 4111 1111 1111 1111
Expiry: Any future date  
CVV: Any 3 digits
```

Happy coding! 🚀
