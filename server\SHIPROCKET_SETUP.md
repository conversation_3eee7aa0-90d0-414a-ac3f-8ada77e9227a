# Shiprocket Configuration Guide

## Quick Setup

1. **Get Shiprocket Account**
   - Sign up at https://shiprocket.in/
   - Complete KYC verification
   - Add pickup locations in your dashboard

2. **Update Environment Variables**
   Edit `server/.env` file:
   ```
   SHIPROCKET_EMAIL=<EMAIL>
   SHIPROCKET_PASSWORD=your-shiprocket-password
   ```

3. **Test Configuration**
   ```bash
   cd server
   node test-shiprocket-config.js
   ```

## How It Works

### Order Creation Flow
1. Customer places order on your site
2. Order data is automatically sent to Shiprocket via API
3. Shiprocket creates shipment and assigns AWB number
4. Order appears in your Shiprocket dashboard with full details

### Order Data Sent to Shiprocket
- Customer billing & shipping details
- Product information (name, SKU, quantity, price)
- Payment method (COD/Prepaid)
- Package dimensions and weight
- Pickup location

### Tracking Updates
- Shiprocket sends status updates via webhook
- Order status automatically updates in your system
- Customer can track shipment using AWB number

## Shiprocket Dashboard

After successful integration, you'll see:
- All orders with complete customer details
- Product information and quantities  
- Payment status (COD/Prepaid)
- Shipping addresses
- Order values and weights
- Tracking information

## Troubleshooting

### Orders not appearing in Shiprocket dashboard:
1. Check environment variables are set correctly
2. Verify Shiprocket account is active and verified
3. Ensure pickup location is configured
4. Check server logs for API errors

### Common Issues:
- **Authentication failed**: Wrong email/password
- **Invalid pickup location**: Set up pickup addresses in Shiprocket dashboard
- **Missing required fields**: Ensure all customer and product data is complete

## Test the Integration

Run the test script to verify everything works:
```bash
cd server
node test-shiprocket-config.js
```

This will:
- Test authentication with your credentials
- Verify pickup locations are configured
- Create a test order to ensure API integration works

## Support

If orders still don't appear in dashboard:
1. Check Shiprocket API logs in your dashboard
2. Verify webhook URL is set (optional)
3. Contact Shiprocket support with your account details
