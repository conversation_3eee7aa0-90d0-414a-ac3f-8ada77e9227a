#!/usr/bin/env node

/**
 * 🚀 Simple Development Server Starter
 * 
 * This script starts your server for local development without ngrok
 * Use this when you don't need webhook testing
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Development Server...\n');

// Function to start the server
function startServer() {
  return new Promise((resolve, reject) => {
    console.log('📦 Starting Express server...');
    const server = spawn('npm', ['run', 'dev'], { 
      stdio: 'inherit',
      shell: true,
      cwd: __dirname
    });

    server.on('close', (code) => {
      if (code !== 0) {
        reject(new Error(`Server process exited with code ${code}`));
      } else {
        resolve();
      }
    });

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n\n🛑 Shutting down...');
      server.kill();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n\n🛑 Shutting down...');
      server.kill();
      process.exit(0);
    });
  });
}

// Main function
async function main() {
  try {
    await startServer();
  } catch (error) {
    console.error('❌ Server failed to start:', error.message);
    process.exit(1);
  }
}

// Start the server
main();
